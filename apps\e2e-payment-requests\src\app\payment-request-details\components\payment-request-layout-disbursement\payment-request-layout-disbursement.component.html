<ng-container
  *ngIf="this.getDisbursementsDetails$ | async as repaymentsDetails"
>
  <button
    (click)="deleteSelectedRows(repaymentsDetails?.paymentRequest?.id)"
    [disabled]="deleteDisabled"
    class="flex ms-7 my-2"
    [ngClass]="{
      hidden: deleteDisabled
    }"
  >
    <mat-icon
      svgIcon="icon-delete"
      class="mat-tm-icon mat-tm-icon-size-15 icon-color icon-color-orientals me-2"
    ></mat-icon>
    <span class="text-sm font-medium">
      {{
        'payment-request.deleteItems'
          | transloco: { length: selectedRows.length }
      }}
    </span>
  </button>

  <tm-no-results
    *ngIf="!repaymentsDetails.paymentRequest?.repayments?.length; else table"
  ></tm-no-results>
  <ng-template #table>
    <div class="table-wrapper">
      <table
        class="w-full"
        aria-label="Disbursement limits table"
        mat-table
        *ngIf="
          !repaymentsDetails.hasNoPaymentRequest &&
          repaymentsDetails.paymentRequest
        "
        [dataSource]="paymentRequestRepayments"
      >
        <ng-container
          matColumnDef="select"
          *ngIf="repaymentsDetails.paymentRequest.status === 'DRAFT'"
        >
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            <!-- [ngClass]="{
                'bg-yellow-300 text-fuchsia-500 ':
                  repaymentsDetails.paymentRequest.status !== 'DRAFT'
              }" -->
            <mat-checkbox
              (change)="toggleAllRows($event)"
              [checked]="
                selectedRows.length === paymentRequestRepayments.length
              "
            ></mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let repayment">
            <mat-checkbox
              [checked]="repayment.selected"
              (change)="onRowSelectionChange(repayment)"
            ></mat-checkbox>
          </td>
        </ng-container>

        <ng-container matColumnDef="loanId">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{ 'payment-request.tableHeaders.loanId' | transloco }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.loanId"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.loanId }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="loanReferenceName">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{ 'payment-request.tableHeaders.loanReferenceName' | transloco }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.loanReferenceName"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.loanReferenceName }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="portfolioReferenceName">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{
              'payment-request.tableHeaders.portfolioReferenceName' | transloco
            }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.portfolioReferenceName"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.portfolioReferenceName }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="portfolioId">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{ 'payment-request.tableHeaders.portfolioId' | transloco }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.portfolioId"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.portfolioId }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="limitName">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{ 'payment-request.tableHeaders.limitName' | transloco }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.limitName"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.limitName }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="limitId">
          <th mat-header-cell *matHeaderCellDef class="max-w-[200px]">
            {{ 'payment-request.tableHeaders.limitId' | transloco }}
          </th>
          <td
            mat-cell
            *matCellDef="let repayment"
            [matTooltip]="repayment.limitId"
          >
            <div class="truncate max-w-[200px]">
              {{ repayment.limitId }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="effectiveDate">
          <th mat-header-cell *matHeaderCellDef class="min-w-[150px]">
            {{ 'payment-request.tableHeaders.effectiveDate' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{ repayment.effectiveDate | date: repaymentsDetails.dateFormat }}
          </td>
        </ng-container>

        <ng-container matColumnDef="customer">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.borrower' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            <div
              class="truncate max-w-[200px]"
              *ngFor="let customer of repayment?.customer?.identityParties"
              [matTooltip]="customer.name"
            >
              {{ customer.name }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="customerId">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.customerId' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            <div
              class="truncate max-w-[200px]"
              *ngFor="let customer of repayment?.customer?.identityParties"
              [matTooltip]="customer.id"
            >
              {{ customer.id }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="customerNationalId">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.customerNationalId' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            <div
              class="truncate max-w-[200px]"
              *ngFor="let customer of repayment?.customer?.identityParties"
              [matTooltip]="customer.nationalId"
            >
              {{ customer.nationalId }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="beneficiary">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.beneficiary' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{ repayment?.beneficiaryName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="beneficiaryId">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.beneficiaryId' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{ repayment?.beneficiaryId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="beneficiaryNationalId">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{
              'payment-request.tableHeaders.beneficiaryNationalId' | transloco
            }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{ repayment?.beneficiaryNationalId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="principal.value">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'payment-request.tableHeaders.principal' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{
              repayment.principal.value | currency: repayment.principal.currency
            }}
          </td>
        </ng-container>

        <ng-container matColumnDef="loanFees.value">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'payment-request.tableHeaders.loanFees' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{
              repayment.loanFees.value | currency: repayment.loanFees.currency
            }}
          </td>
        </ng-container>

        <ng-container matColumnDef="netAmount.value">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'payment-request.tableHeaders.netAmount' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{
              repayment.netAmount.value | currency: repayment.netAmount.currency
            }}
          </td>
        </ng-container>

        <ng-container matColumnDef="paymentMethod">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.paymentMethod' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            {{ repayment.paymentMethod }}
          </td>
        </ng-container>

        <ng-container matColumnDef="paymentDetails">
          <th mat-header-cell *matHeaderCellDef class="min-w-[200px]">
            {{ 'payment-request.tableHeaders.paymentDetails' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment">
            <ng-container *ngIf="repayment.bankAccount">
              {{ repayment.bankAccount.bankCode }}/{{
                repayment.bankAccount.bankBranch
              }}/{{ repayment.bankAccount.accountNumber }}
            </ng-container>
          </td>
        </ng-container>

        <ng-container matColumnDef="view" stickyEnd>
          <th
            mat-header-cell
            *matHeaderCellDef
            class="w-12"
            [ngClass]="{ 'max-w-0 p-0': (getShowLoans$ | async) === false }"
          >
            {{ 'payment-request.tableHeaders.actions' | transloco }}
          </th>
          <td mat-cell *matCellDef="let repayment" class="bg-carbon w-12">
            <div class="flex justify-center">
              {{ repayment.paymentStatus }}
              <e2e-payment-menu
                [paymentId]="repayment.loanId"
                [paymentRequestId]="repaymentsDetails.paymentRequest.id"
                [paymentStatus]="repaymentsDetails.paymentRequest.status"
              >
              </e2e-payment-menu>
            </div>
          </td>
        </ng-container>

        <ng-container
          *ngIf="
            getSelectedRepaymentsTableColumns$
              | async as selectedRepaymentsTableColumns
          "
        >
          <tr
            mat-header-row
            *matHeaderRowDef="selectedRepaymentsTableColumns"
          ></tr>
          <tr
            mat-row
            *matRowDef="let repayment; columns: selectedRepaymentsTableColumns"
          ></tr>
        </ng-container>
      </table>
    </div>

    <mat-paginator
      *ngIf="
        !repaymentsDetails.hasNoPaymentRequest &&
        repaymentsDetails.paymentRequest
      "
      [pageIndex]="repaymentsDetails.paymentRequest.pageNumber"
      [pageSize]="repaymentsDetails.paymentRequest.pageSize"
      [pageSizeOptions]="pageSizeOptions"
      [length]="repaymentsDetails.paymentRequest.totalItems"
      (page)="pageEvent($event)"
      class="mt-2"
    >
    </mat-paginator>
  </ng-template>
</ng-container>
