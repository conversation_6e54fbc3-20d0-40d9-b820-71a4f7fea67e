<ng-container *ngIf="getCreditLimitDetails$ | async as creditLimitDetails">
  <ng-container *ngIf="getDateFormat$ | async as dateFormat">
    <div class="text-compass-87 font-medium text-2xl mb-6">
      {{ 'credit-limit-details.matTabLinks.details' | transloco }}
    </div>

    <div class="font-black text-sm text-compass mt-6 mb-2.5">
      <span>
        {{ 'e2e.details.general' | transloco | uppercase }}
      </span>
    </div>
    <div class="grid grid-cols-2 3xl2:grid-cols-3 gap-4">
      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.referenceName' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.referenceName }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.limitAmount' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{
            creditLimitDetails.amount.value
              | currency: creditLimitDetails.amount.currency
          }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.contractNumber' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.contractReferenceNumber }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.signDate' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.contractSignatureDate | date: dateFormat }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.effectiveDate' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.effectiveDate | date: dateFormat }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.period' | transloco }}
        </div>
        <div
          *ngIf="creditLimitDetails.period"
          class="text-compass font-semibold"
        >
          {{ creditLimitDetails.period }}
          {{ 'e2e.periods.months' | transloco }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.endDate' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.endDate | date: dateFormat }}
        </div>
      </div>

      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.description' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.description }}
        </div>
      </div>
    </div>

    <!-- <ng-container *ngIf=""> -->
    <mat-divider class="!mt-6 border-compass-22"></mat-divider>
    <div class="font-black text-sm text-compass mt-6 mb-2.5">
      <span>
        {{ 'e2e.details.keyIndicators' | transloco | uppercase }}
      </span>
    </div>
    <div class="grid grid-cols-3 gap-4">
      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.pti' | transloco | uppercase }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.pti | number: '1.2-2' | removeTrailingZeros }}%
        </div>
      </div>
      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.originalLtv' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.ltv | number: '1.2-2' | removeTrailingZeros }}%
        </div>
      </div>
      <div class="text-sm">
        <div class="text-compass-60">
          {{ 'e2e.details.originMarketValueCoverage' | transloco }}
        </div>
        <div class="text-compass font-semibold">
          {{ creditLimitDetails.originalMarketValueCoverage }}%
        </div>
      </div>
    </div>
    <!-- </ng-container> -->

    <mat-divider class="!mt-6 border-compass-22"></mat-divider>

    <div class="font-black text-sm text-compass mt-6 mb-2.5">
      <span>
        {{ 'e2e.details.documents' | transloco | uppercase }}
      </span>
    </div>

    <div class="grid grid-cols-2 gap-4 items-center">
      <div>
        <e2e-attached-documents
          class="text-sm"
          *ngIf="getCreditLimitAllDocuments$ | async as documents"
          [documents]="documents"
          [showAllTypes]="true"
        ></e2e-attached-documents>
      </div>

      <tm-upload-documents
        (uploadDocument)="uploadDocument($event)"
      ></tm-upload-documents>
    </div>
  </ng-container>
</ng-container>
