{"title": "Application", "decisionMaker": {"title": "Application status", "applicationDetails": "Application details", "resolution": "Resolution", "resolutionMandatory": "Resolution text is mandatory", "APPROVEDTitle": "Approve application", "APPROVEDMessage": "You are about to approve the application", "REJECTEDTitle": "Reject application", "REJECTEDMessage": "You are about to reject the application", "CANCELEDTitle": "Cancel application", "CANCELEDMessage": "You are about to cancel the application", "FAILEDTitle": "Retry application", "FAILEDMessage": "You are about to retry the application", "dialogButtons": {"APPROVED": "Approve", "REJECTED": "Reject", "CANCELED": "Cancel Application", "FAILED": "Retry Application"}}, "matTabLinks": {"changeRequest": "Change request", "borrower": "<PERSON><PERSON><PERSON>", "parties": "Parties", "portfolios": "Portfolio", "product": "Product", "request": "Request"}, "borrower": {"title": "Borrower details"}, "overview": {"appliedOn": "Applied on", "applicationType": "Application type", "requestedBy": "Requested by", "referenceNumber": "Contract reference nr."}, "products": {"title": "Product details", "noProducts": "There are no products added."}, "portfolios": {"title": "Portfolio details", "noPortfolios": "There are no portfolios added."}, "approval": {"title": "Approvals", "requested": "Requested:"}, "credit-limit-early-repayment": {"name": "Credit limit early repayment request", "creditLimitDetails": "Credit limit details", "referenceName": "Reference name", "status": "Status", "effectiveDate": "Effective date", "endUtilizationDate": "End utilization date", "maturityDate": "Maturity date", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount", "repaymentAmount": "Repayment amount", "limitAmount": "Limit amount", "repaymentType": "Repayment type", "requestDate": "Request date", "fees": "Fees", "repaymentDistribution": "Repayment distribution", "moneyAllocation": "Money allocation", "partial": "Partial", "full": "Full", "overdue": "Overdue", "paymentMethod": "Payment method", "account": "Account", "payerName": "Payer name", "overdueRepayment": "Overdue repayment", "product": "Product", "requestSimulation": "Request simulation", "totalRepayment": "Total repayment", "overdueAmount": "Overdue amount", "principal": "Principal", "earlyRepaymentFee": "Early repayment fee", "interest": "Interest", "interestAmount": "Interest amount", "matTabLinks": {"simulation": "Simulation"}, "portfolioTooltip": {"overdueAmount": "Overdue Amount", "portfolioAmount": "Portfolio amount", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount", "purpose": "Purpose", "effectiveDate": "Effective date", "endUtilizationDate": "End utilization date", "maturityDate": "Maturity date"}, "trackTooltip": {"trackAmount": "Track Amount", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount"}, "loanTooltip": {"loanAmount": "<PERSON><PERSON>", "outstandingPrincipal": "Outstanding principal", "amountFullRepayment": "Amount full repayment", "disbursementDate": "Disbursement date", "maturityDate": "Maturity date", "status": "Status", "numberOfPayments": "Number of payments", "interestType": "Interest Type", "regularInterestRate": "Regular interest rate", "financialSource": "Financial source"}}}