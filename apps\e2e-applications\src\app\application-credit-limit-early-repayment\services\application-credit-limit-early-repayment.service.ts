import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  ApplicationDecisionRequest,
  FeeTypes,
  LoanDetail,
  LoanEarlyRepaymentFeeSimulate,
} from '@e2e/lib/types';
import { ENVIRONMENT, Environment } from '@tenant-management/lib/environments';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ApplicationCreditLimitEalyRepaymentService {
  constructor(
    private http: HttpClient,
    @Inject(ENVIRONMENT) private environment: Environment
  ) {}

  sendApplicationDecision(
    id: string,
    applicationDecision: ApplicationDecisionRequest
  ): Observable<ApplicationDecisionRequest> {
    return this.http.put<ApplicationDecisionRequest>(
      `${this.environment.apiPrefix}/v1/requests/CreditLimitEarlyRepayment/${id}/approval/status`,
      applicationDecision
    );
  }

  getFullRepaymentSimulate(
    loanId: string | number,
    effectiveDate: Date | string,
    fees: LoanEarlyRepaymentFeeSimulate[]
  ): Observable<LoanDetail> {
    // const todayDate = format(new Date(), dateRequestFormat);
    const params: any = {
      loanId,
      effectiveDate,
    };

    // EXCLUDE EARLY_REPAYMENT_OPERATIONAL_FEE
    const filteredArray = fees.filter(
      (item: any) => item.type !== FeeTypes.EarlyRepaymentOperationalFee
    );
    if (filteredArray) {
      params.fees = filteredArray;
    }

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/requests/full-early-repayment/simulate`,
      {
        ...params,
      }
    );
  }

  getPartialRepaymentSimulate(
    loanId: string | number,
    effectiveDate: Date | string,
    fees: LoanEarlyRepaymentFeeSimulate[],
    earlyRepaymentAmount: any,
    spreadMethod: any
  ): Observable<LoanDetail> {
    const params: any = {
      loanId,
      effectiveDate,
      earlyRepaymentAmount,
      spreadMethod,
    };

    // EXCLUDE EARLY_REPAYMENT_OPERATIONAL_FEE
    const filteredArray = fees.filter(
      (item: any) => item.type !== FeeTypes.EarlyRepaymentOperationalFee
    );
    if (filteredArray) {
      params.fees = filteredArray;
    }

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/requests/partial-early-repayment/simulate`,
      {
        ...params,
      }
    );
  }

  getDebtRepaymentSimulate(
    limitId: any,
    effectiveDate: any,
    amount: any
  ): Observable<LoanDetail> {
    const params: any = {
      limitId,
      effectiveDate,
      amount,
    };

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/credit-limits/debt-charge-simulate`,
      {
        ...params,
      }
    );
  }

  getRequestSimulate(
    limitId: any,
    spreadMethod: any,
    effectiveDate: any,
    loans: any,
    fees: any
  ): Observable<LoanDetail> {
    const params: any = {
      limitId,
      spreadMethod,
      effectiveDate,
      loans,
      fees,
    };

    return this.http.post<LoanDetail>(
      `${this.environment.apiPrefix}/v1/credit-limits/early-repayment-simulate`,
      {
        ...params,
      }
    );
  }
}
