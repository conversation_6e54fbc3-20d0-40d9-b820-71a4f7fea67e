import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabId } from '@tenant-management/lib/types';
import { CreditLimitChangeAmountComponent } from './components/credit-limit-change-amount/credit-limit-change-amount.component';
import { CreditLimitCollateralsComponent } from './components/credit-limit-collaterals/credit-limit-collaterals.component';
import { CreditLimitConsumersComponent } from './components/credit-limit-consumers/credit-limit-consumers.component';
import { CreditLimitDetailsLayoutComponent } from './components/credit-limit-details-layout/credit-limit-details-layout.component';
import { CreditLimitDetailsComponent } from './components/credit-limit-details/credit-limit-details.component';
import { CreditLimitEarlyRepaymentComponent } from './components/credit-limit-early-repayment/credit-limit-early-repayment.component';
import { CreditLimitLoansComponent } from './components/credit-limit-loans/credit-limit-loans.component';
import { CreditLimitOwnerComponent } from './components/credit-limit-owner/credit-limit-owner.component';
import { CreditLimitPortfolioChangeAmountComponent } from './components/credit-limit-portfolio-change-amount/credit-limit-portfolio-change-amount.component';
import { CreditLimitPortfolioChangeDateComponent } from './components/credit-limit-portfolio-change-date/credit-limit-portfolio-change-date.component';
import { CreditLimitPortfolioChangeEffectiveDateComponent } from './components/credit-limit-portfolio-change-effective-date/credit-limit-portfolio-change-effective-date.component';
import { CreditLimitPortfolioDisbursementComponent } from './components/credit-limit-portfolio-disbursement/credit-limit-portfolio-disbursement.component';
import { CreditLimitPortfolioEffectiveDateComponent } from './components/credit-limit-portfolio-effective-date/credit-limit-portfolio-effective-date.component';
import { CreditLimitPortfoliosComponent } from './components/credit-limit-portfolios/credit-limit-portfolios.component';
import { CreditLimitSimulateComponent } from './components/credit-limit-simulate/credit-limit-simulate.component';
import { EarlyRepaymentLayoutComponent } from './components/early-repayment-layout/early-repayment-layout.component';
import { LetterOfIntentComponent } from './components/letter-of-intent/letter-of-intent.component';
import { creditLimitChangeAmountGuard } from './guards/credit-limit-change-amount.guard';
import { creditLimitChangeDateGuard } from './guards/credit-limit-change-date.guard';
import { creditLimitChangeEffectiveDateGuard } from './guards/credit-limit-change-effective-date.guard';
import { CreditLimitConsumersGuard } from './guards/credit-limit-consumers.guard';
import { creditLimitDisbursementGuard } from './guards/credit-limit-disbursement.guard';
import { creditLimitLoansGuard } from './guards/credit-limit-loans.guard';
import { CreditLimitOwnerGuard } from './guards/credit-limit-owner.guard';
import { creditLimitSetEffectiveDateGuard } from './guards/credit-limit-set-effective-date.guard';
import { creditLimitSetIdGuard } from './guards/credit-limit-set-id.guard';

const parentTabIdData = {
  data: {
    parentTabId: TabId.Operations,
  },
};

const routes: Routes = [
  {
    path: 'early-repayment',
    ...parentTabIdData,
    component: EarlyRepaymentLayoutComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'details',
      },
      {
        path: 'details',
        ...parentTabIdData,
        component: CreditLimitEarlyRepaymentComponent,
      },
      {
        path: 'simulate',
        ...parentTabIdData,
        component: CreditLimitSimulateComponent,
      },
    ],
  },
  // {
  //   path: 'aa/:limitId/early-repayment',
  //   ...parentTabIdData,
  //   component: CreditLimitSimulateComponent,
  // },
  // {
  //   path: 'aa/:limitId/',
  //   ...parentTabIdData,
  //   component: CreditLimitSimulateComponent,
  // },

  // {
  //   path: 'early-repayment',
  //   ...parentTabIdData,
  //   component: CreditLimitDetailsLayoutComponent,
  //   // canActivate: [creditLimitChangeAmountGuard],
  //    children: [
  //     {
  //       path: '',
  //       pathMatch: 'full',
  //       redirectTo: 'early-repayment',
  //     },
  //     {
  //       path: 'early-repayment',
  //       ...parentTabIdData,
  //       component: CreditLimitEarlyRepaymentComponent,
  //     }
  //   ]
  // },
  // {
  //   path: 'simulate',
  //   ...parentTabIdData,
  //   component: CreditLimitEarlyRepaymentComponent,
  //   // canActivate: [creditLimitChangeAmountGuard],
  // },

  {
    path: '',
    component: CreditLimitDetailsLayoutComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'details',
      },
      {
        path: 'details',
        ...parentTabIdData,
        component: CreditLimitDetailsComponent,
      },
      {
        path: 'portfolios',
        ...parentTabIdData,
        component: CreditLimitPortfoliosComponent,
        children: [
          {
            path: ':portfolioId/disbursement',
            ...parentTabIdData,
            component: CreditLimitPortfolioDisbursementComponent,
            canActivate: [creditLimitSetIdGuard, creditLimitDisbursementGuard],
          },
          {
            path: ':portfolioId/effective-date',
            ...parentTabIdData,
            component: CreditLimitPortfolioEffectiveDateComponent,
            canActivate: [
              creditLimitSetIdGuard,
              creditLimitSetEffectiveDateGuard,
            ],
          },
          {
            path: ':portfolioId/change-effective-date',
            ...parentTabIdData,
            component: CreditLimitPortfolioChangeEffectiveDateComponent,
            canActivate: [
              creditLimitSetIdGuard,
              creditLimitChangeEffectiveDateGuard,
            ],
          },
          {
            path: ':portfolioId/change-date',
            ...parentTabIdData,
            component: CreditLimitPortfolioChangeDateComponent,
            canActivate: [creditLimitSetIdGuard, creditLimitChangeDateGuard],
          },
          {
            path: ':portfolioId/change-amount',
            ...parentTabIdData,
            component: CreditLimitPortfolioChangeAmountComponent,
            canActivate: [creditLimitSetIdGuard],
          },
        ],
      },
      {
        path: 'borrower',
        ...parentTabIdData,
        component: CreditLimitOwnerComponent,
        canActivate: [CreditLimitOwnerGuard],
      },
      {
        path: 'consumers',
        ...parentTabIdData,
        component: CreditLimitConsumersComponent,
        canActivate: [CreditLimitConsumersGuard],
      },
      {
        path: 'collaterals',
        ...parentTabIdData,
        component: CreditLimitCollateralsComponent,
      },
      {
        path: 'change-amount',
        ...parentTabIdData,
        component: CreditLimitChangeAmountComponent,
        outlet: 'actions-side-panel',
        canActivate: [creditLimitChangeAmountGuard],
      },
      {
        path: 'letter-of-intent',
        ...parentTabIdData,
        component: LetterOfIntentComponent,
        outlet: 'actions-side-panel',
        canActivate: [],
      },
      {
        path: 'simulate',
        ...parentTabIdData,
        component: CreditLimitEarlyRepaymentComponent,
        // canActivate: [creditLimitChangeAmountGuard],
      },
      {
        path: 'loans',
        ...parentTabIdData,
        component: CreditLimitLoansComponent,
        canActivate: [creditLimitLoansGuard],
      },
      {
        path: '**',
        redirectTo: 'details',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CreditLimitDetailsRoutingModule {}
