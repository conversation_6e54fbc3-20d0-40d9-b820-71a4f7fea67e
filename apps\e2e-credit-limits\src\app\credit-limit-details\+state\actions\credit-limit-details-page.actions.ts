import {
  CreditLimitChangeAmount,
  CreditLimitEarlyRepayment,
  E2eEarlyRepaymentPortfolio,
  EarlyRepaymentTypes,
  LoanChangeAmount,
  LoanChangeDate,
  LoanDisbursement,
  LoanEarlyRepaymentFeeSimulate,
  LoanEffectiveDate,
  PortfoliosSort,
} from '@e2e/lib/types';
import { createAction, props } from '@ngrx/store';
import { TmRegisterDocument } from '@tenant-management/lib/types';
import { CollateralNewInsurance } from '../../models/collateral-new-insurance';
import { CreditLimitDetails } from '../../models/credit-limit-details';

export const setCreditLimitId = createAction(
  '[Credit Limit Details PAGE] Set initial credit limits',
  props<{ limitId: any }>()
);

export const getCreditLimitDetails = createAction(
  '[Credit Limit Details PAGE] Get credit limit details',
  props<{ creditLimits: CreditLimitDetails }>()
);

export const getCreditLimitOwner = createAction(
  '[Credit Limit Details PAGE] Get credit limit owner'
);

export const getCreditLimitConsumers = createAction(
  '[Credit Limit Details PAGE] Get credit limit consumers'
);

export const sendLimitDetailsFilestackDocument = createAction(
  '[Credit Limit Details PAGE] Send limit details filestack document',
  props<{ document: TmRegisterDocument }>()
);

export const sendChangeAmount = createAction(
  '[Credit Limit Details PAGE] Send change amount',
  props<{ changeAmount: CreditLimitChangeAmount }>()
);

export const cancelCreditLimit = createAction(
  '[Credit Limit Details PAGE] Cancel credit limit'
);

export const getCreditLimitPortfolios = createAction(
  '[Credit Limit Details PAGE] Get credit limit portfolios'
);

export const getCreditLimitPortfolioDetails = createAction(
  '[Credit Limit Details PAGE] Get credit limit portfolio details',
  props<{ portfolioIndex: number; portfolioId: string }>()
);

export const getBorrowerPortfolioLoans = createAction(
  '[Credit Limit Details portfolios PAGE] Get credit limit portfolio loans',
  props<{ portfolioId: string }>()
);

export const getTrackLoans = createAction(
  '[Credit Limit Details PAGE] Get track loans',
  props<{
    trackId: string;
    productTrackReferenceName: string;
  }>()
);

export const getBorrowerPortfolioProducts = createAction(
  '[Credit Limit Details portfolios PAGE] Get credit limit portfolio products',
  props<{
    portfolioReferenceName: string;
    portfolioId: string;
    trackId: string | undefined;
  }>()
);

export const setCreditLimitCurrentPortfolioId = createAction(
  '[Credit Limit Details portfolios PAGE] Set current portfolio id',
  props<{ creditLimitCurrentPortfolioId: string }>()
);

export const sendDisbursement = createAction(
  '[Credit Limit Details portfolios disbursement PAGE] Send disbursement',
  props<{ disbursement: LoanDisbursement }>()
);
export const sendChangeDate = createAction(
  '[Credit Limit Details portfolios change date PAGE] Send change date',
  props<{ changeDate: LoanChangeDate }>()
);

export const sendEffectiveDate = createAction(
  '[Credit Limit Details portfolios effective date PAGE] Send effective date',
  props<{ effectiveDate: LoanEffectiveDate }>()
);

export const sendChangeEffectiveDate = createAction(
  '[Credit Limit Details portfolios change effective date PAGE] Send change effective date',
  props<{ effectiveDate: LoanEffectiveDate }>()
);

export const sendChangePortfolioAmount = createAction(
  '[Credit Limit Details portfolios change amount PAGE] Send change amount',
  props<{ changeAmount: LoanChangeAmount }>()
);

export const sortPortfolios = createAction(
  '[Credit limit portfolios PAGE] Sort portfolios',
  props<{ portfoliosSort: PortfoliosSort }>()
);

export const getPrepayments = createAction(
  '[Credit Limit Details portfolios PAGE] Get prepayments',
  props<{ portfolioId: string }>()
);

export const getCollaterals = createAction(
  '[Credit Limit Collaterals PAGE] Get collaterals'
);

export const selectCollateral = createAction(
  '[Credit Limit Collaterals PAGE] Select collateral',
  props<{ collateralId: string }>()
);

export const selectCollateralOwner = createAction(
  '[Credit Limit Collaterals PAGE] Select collateral owner',
  props<{ collateralOwnerId: string }>()
);

export const createNewInsurance = createAction(
  '[Credit Limit Collaterals PAGE] Create new insurance',
  props<{ newInsurance: CollateralNewInsurance }>()
);

export const getFees = createAction('[Credit Limit PAGE] Get fees');

export const getOverview = createAction(
  '[Credit Limit PAGE] Get getOverview',
  props<{ effectiveDate: Date; fees: any }>()
);

// export const selectPortfolio = createAction(
//   '[Credit Limit Details Portfolio] Select Portfolio',
//   props<{ portfolio: E2eEarlyRepaymentPortfolio; mode?: EarlyRepaymentTypes }>()
// );

export const selectPortfolio = createAction(
  '[Credit Limit Details Portfolio] Select Portfolio',
  props<{
    portfolio: E2eEarlyRepaymentPortfolio;
    mode?: EarlyRepaymentTypes | null;
    trackStates?: {
      [trackId: string]: { selected: boolean; mode: EarlyRepaymentTypes };
    };
    loanStates?: {
      [loanId: number]: {
        selected: boolean;
        mode: EarlyRepaymentTypes;
        amount?: number;
      };
    };
  }>()
);

export const deselectPortfolio = createAction(
  '[Credit Limit Details Portfolio] Deselect Portfolio',
  props<{ portfolioId: string }>()
);

export const changePortfolioMode = createAction(
  '[Credit Limit Details Portfolio] Change Portfolio Mode',
  props<{ portfolioId: string; mode: EarlyRepaymentTypes }>()
);

export const updatePortfolioMode = createAction(
  '[Early Repayment] Update Portfolio Mode',
  props<{ portfolioId: string; mode: EarlyRepaymentTypes | null }>()
);

export const updateTrackMode = createAction(
  '[Early Repayment] Update Track Mode',
  props<{
    portfolioId: string;
    trackId: string;
    mode: EarlyRepaymentTypes | null;
  }>()
);

export const updateLoanModeAndAmount = createAction(
  '[Early Repayment] Update Loan Mode and Amount',
  props<{
    portfolioId: string;
    trackId: string;
    loanId: number;
    mode: EarlyRepaymentTypes | null;
    amount?: number;
  }>()
);

export const triggerSimulationRequest = createAction(
  '[Early Repayment Layout] Trigger Simulation Request'
);

export const clearSimulationRequest = createAction(
  '[Early Repayment Layout] Trigger Clear Simulation Request'
);

export const sendSimulationRequest = createAction(
  '[Early Repayment Layout] Trigger Send Simulation Request',
  props<{
    earlyRepaymentSimulationRequest: any;
  }>()
);

export const setEarlyRepaymentForm = createAction(
  '[Credit Limit PAGE] Set Early Repayment Form',
  props<{ earlyRepaymentForm: CreditLimitEarlyRepayment }>()
);

export const sendEarlyRepaymentRequest = createAction(
  '[Credit Limit Details PAGE] Send Early Repayment Request',
  props<{
    portfolioIds: any;
    earlyRepaymentForm: CreditLimitEarlyRepayment;
    fees: any;
  }>()
);

export const updateTotalSelectedLoanAmount = createAction(
  '[Credit Limit Details PAGE] Update Total Selected Loan Amount',
  props<{ amount: any }>()
);

export const setAmountToRepay = createAction(
  '[Credit Limit PAGE] Set amount to repay',
  props<{ amount: any }>()
);

export const setIsFormValid = createAction(
  '[Credit Limit PAGE] Set IsFormValid',
  props<{ isFormValid: boolean }>()
);

export const resetCreditLimitEarlyRepaymentState = createAction(
  '[Credit Limit PAGE] Reset credit limit details state'
);

export const getLoanFullRepayments = createAction(
  '[Credit Limit early repayment PAGE] Get loan full repayment simulation',
  props<{
    loanId: any;
    effectiveDate: Date | string;
    fees: LoanEarlyRepaymentFeeSimulate[];
  }>()
);

export const getLoanPartialRepayments = createAction(
  '[Credit Limit early repayment PAGE] Get loan partial repayment simulation',
  props<{
    loanId: any;
    effectiveDate: Date | string;
    fees: LoanEarlyRepaymentFeeSimulate[];
    earlyRepaymentAmount: any;
    spreadMethod: any;
  }>()
);

export const getLoanDebtRepayments = createAction(
  '[Credit Limit early repayment PAGE] Get debt repayment simulation',
  props<{
    limitId: any;
    effectiveDate: Date | string;
    amount: any;
  }>()
);

export const getRequestSimulate = createAction(
  '[Credit Limit early repayment PAGE] Get Request Simulate',
  props<{
    limitId: any;
    spreadMethod: any;
    effectiveDate: any;
    loans: any;
    fees: any;
  }>()
);

export const getCreditLimitLoans = createAction(
  '[Credit Limit Loans PAGE] Get credit limit loans'
);

export const generateLetterOfIntent = createAction(
  '[Credit Limit PAGE] Generate letter of intent',
  props<{
    letterOfIntent: {
      valueDate: string;
      organizationName: string;
    };
  }>()
);

export const clearCreditLimitEarlyRepaymentSimulate = createAction(
  '[Credit Limit early repayment PAGE] Clear credit limit early repayment simulate'
);
