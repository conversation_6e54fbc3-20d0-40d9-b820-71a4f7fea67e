{"operationStatuses": {"APPROVED": "Approved", "REJECTED": "Rejected", "INITIATED": "Initiated", "PENDING_APPROVAL": "Pending approval", "APPROVAL_IN_PROGRESS": "Approval in progress", "PENDING": "Pending", "CANCELED": "Canceled", "COMPLETED": "Completed", "FAILED": "Failed", "PREVIEW": "Preview", "ACTIVE": "Active", "PENDING_DISBURSEMENT": "Pending disb.", "FAILED_DISBURSEMENT": "Failed disb.", "REPAID": "Repaid", "PRE_ACTIVE": "Pre active", "CLOSED": "Closed", "CANCELLED": "Canceled", "PRE_UTILIZED": "Pre utilized", "PRE_CANCELLED": "Pre cancelled", "PRE_CLOSED": "Pre closed", "BLOCKED": "Blocked", "PENDING_TRANSFER": "Pending transfer", "TRANSFERRED": "Transferred", "PAYMENTS": "Payments", "PAST_DELINQUENCY": "Past delinquency", "ORIGINAL_END_DATE": "Original end date", "EARLY_REPAYMENT": "Early repayment", "RECYCLED": "Recycled", "ARREARS": "Arrears", "NO_LONGER_NEEDED": "No longer needed", "OUT_OF_STOCK": "Out of stock", "FAILED_ON_DELIVERY": "Failed on delivery", "ARRIVED_DAMAGED": "Arrived damaged", "NOT_AS_DESCRIBED": "Not as described", "WRONG_PRODUCT_OR_SIZE": "Wrong product or size", "BETTER_PRICE_ELSEWHERE": "Better price elsewhere", "FULLY_REPAID": "Fully repaid", "PENDING_FREEZE": "Pending freezed", "FREEZED": "Freezed", "GRACE_PERIOD": "Grace period", "LATE": "Late", "DELINQUENT": "Delinquent", "PERSONAL": "Personal", "BUSINESS": "Business", "DRAFT": "Draft", "CHARGED": "Charged", "CHARGING": "Charging", "RECALCULATING": "Recalculating", "LOAN_APPLICATION": "New deal", "DISBURSEMENT": "Disbursement", "SET_PORTFOLIO_EFFECTIVE_DATE": "Set portfolio effective date", "CHANGE_PORTFOLIO_EFFECTIVE_DATE": "Change portfolio effective date", "CHANGE_PORTFOLIO_END_UTILIZATION_DATE": "Change portfolio end utilization date", "CHANGE_PORTFOLIO_AMOUNT": "Change portfolio amount", "FREEZE_LOAN": "Freeze loan", "UNFREEZE_LOAN": "Unfreeze loan", "PARTIAL_EARLY_REPAYMENT": "Partial early repayment", "FULL_EARLY_REPAYMENT": "Full early repayment", "PAYMENT_METHOD": "Payment method", "CHANGE_PAYMENT_DAY": "Change payment day", "NEW_CREDIT_LIMIT": "New credit limit", "CHANGE_CREDIT_LIMIT_AMOUNT": "Change credit limit amount", "UTILIZED": "Utilized", "CREATED": "Created", "PROCESSING": "Processing", "EWA_WITHDRAWAL": "<PERSON><PERSON><PERSON>", "MORTGAGE_NEW_DEAL": "Mortgage new deal", "MORTGAGE_DISBURSEMENT": "Mortgage disbursement", "CREDIT_LIMIT_EARLY_REPAYMENT": "Credit limit early repayment", "CREDIT_LIMIT_LETTER_OF_INTENT": "Letter of intent"}, "paymentStatuses": {"COMPLETED": "COMPLETED", "PROCESSING": "PROCESSING", "APPROVED": "APPROVED", "CANCELED": "CANCELED", "DRAFT": "DRAFT", "FAILED": "FAILED", "RECALCULATING": "RECALCULATING", "INITIATED": "INITIATED", "PREVIEW": "PREVIEW", "CHARGED": "CHARGED"}, "monthlyReportStatuses": {"GENERATED": "Generated", "SENT": "<PERSON><PERSON>", "APPROVED": "Approved", "PROCESSING": "Processing", "FAILED": "Failed", "CANCELED": "Canceled"}, "primaryPeriods": {"disbursementDateRange": "Disbursement date", "previousRepaymentDateRange": "Previous repayment date", "nextRepaymentDateRange": "Next repayment date", "effectiveDateRange": "Effective date", "endDateRange": "End date"}, "secondaryPeriods": {"YESTERDAY": "Yesterday", "LAST_7_DAYS": "Last 7 days", "LAST_30_DAYS": "Last 30 days", "THIS_WEEK": "This week", "CUSTOM": "Custom"}, "applicationCalendarTypes": {"ANY_DAY": "Any day", "BUSINESS_DAY": "Business day"}, "types": {"TODAY": "Today", "NEXT_MONTH": "Next month", "RELATIVE_TO_DISBURSEMENT_DATE": "Relative to disbursement date", "SPECIFIC_DATE": "Specific date", "TYPE_30_360": "Type 30/360", "ACTUAL_ACTUAL": "Actual/Actual", "ACTUAL_360": "Actual 360"}, "applicationFeeTypes": {"PORTFOLIO_CREATION_FEE": "Creation fee", "CREATION_TOLL": "Creation toll", "DISBURSEMENT_FEE": "Disbursement fee", "MAINTENANCE_FEE": "Maintenance fee", "REFINANCE_FEE": "Refinance fee", "UNUTILIZED_FEE": "Unutilized fee", "CHANGE_PAYMENT_DAY_OPERATIONAL_FEE": "Change payment day operational fee", "EARLY_REPAYMENT_EARLY_NOTICE_FEE": "Early repayment early notice fee", "EARLY_REPAYMENT_OPERATIONAL_FEE": "Early repayment operational fee", "AVERAGE_INDEX_FEE": "Average index fee", "EARLY_REPAYMENT_CAPITALIZATION_FEE": "Early repayment capitalization fee", "FREEZE_OPERATIONAL_FEE": "Freeze operational fee", "UNFREEZE_OPERATIONAL_FEE": "Unfreeze operational fee", "CHANGE_LOAN_CONDITIONS_FEE": "Change loan conditions fee", "EARLY_REPAYMENT_MAINTENANCE_FEE": "Early repayment fee", "ARREARS_LEGAL_FEES": "Appears legal fees", "MORTGAGE_REGISTRATION": "Mortgage registration", "PRODUCE_LAND_REGISTRY_EXTRACT": "Product loan registry extract", "RETURNED_DEBIT_PAYMENT": "Returned debit payment"}, "feeBaseTypes": {"DISBURSED_AMOUNT": "Disbursement amount", "OUTSTANDING_AMOUNT": "Outstanding amount", "PORTFOLIO_AMOUNT": "Portfolio amount", "UNUTILIZED_AMOUNT": "Unutilized amount", "REPAID_PRINCIPAL_AMOUNT": "Repaid principal amount", "PAYMENT_AMOUNT": "Payment amount", "LIMIT_AMOUNT": "Limit amount"}, "filters": {"filterResults": "Filter results", "filterByStatuses": "Filter by statuses", "filterBySubStatuses": "Filter by sub statuses", "filterByType": "Filter by type", "filterByBorrower": "Type of borrower", "filterByDate": "Filter by date", "selectDate": "Filter by date", "all": "All", "from": "From", "to": "To", "reportPeriodTitle": "Report period"}, "paymentScheduleTypes": {"SHPITSER": "Spitzer", "FIX_PRINCIPAL": "Fix principal", "BULLET": "Bullet", "UNEVEN_PAYMENTS": "Uneven payments", "CUSTOM": "Custom"}, "paymentMethods": {"CREDIT_CARD": "Credit card", "DEBIT_CARD": "Debit card", "DIRECT_DEBIT": "Direct debit", "VOUCHER": "Voucher", "BANK_ACCOUNT": "Bank account", "MANUAL": "Manual"}, "beneficiaryTypes": {"SELLER_MORTGAGE_LETTER_OF_INTENT": "Seller mortgage letter of intent", "SELLER": "<PERSON><PERSON>", "TRUST_ACCOUNT": "Trust account", "CONTRACTOR": "Contractor", "EMPLOYEE": "Contractor"}, "paymentFrequencies": {"MONTHLY": "Monthly", "DAILY": "Daily", "QUARTERLY": "Quarterly", "SEMI_ANNUAL": "Semi annual", "YEARLY": "Yearly", "CUSTOM": "Custom"}, "loanInterestTypes": {"FIXED": "Fixed", "VARIABLE": "Variable"}, "loanEntryTypes": {"PMT": "Installment", "PER": "Partial early repayment", "FER": "Full early repayment", "DIS": "Disbursement"}, "insuranceTypes": {"AUTO_INSURANCE": "Auto insurance", "PROPERTY_INSURANCE": "Property insurance", "RENTERS_INSURANCE": "Renters insurance", "BUSINESS_INSURANCE": "Business insurance", "LIABILITY_INSURANCE": "Liability insurance", "INVENTORY_INSURANCE": "Inventory insurance", "AIRCRAFT_INSURANCE": "Aircraft insurance", "BOAT_INSURANCE": "Boat insurance", "CROP_INSURANCE": "Crop insurance", "ART_AND_COLLECTIBLES_INSURANCE": "Art and collectibles insurance", "LIFE_INSURANCE": "Life insurance"}, "lifeInsurance": "Life insurance", "insurance": {"title": "Insurance details", "create": "Create new insurance", "details": "Details", "insuranceAmount": "Insurance amount", "policyType": "Policy type", "policyId": "Policy ID", "policyStartingDate": "Policy starting date", "policyExpiryDate": "Policy expiry date", "insuranceCompanyName": "Insurance company name", "insuranceDocuments": "Insurance documents", "addInsuranceBtn": "Add another insurance", "limitRefName": "Limit ref. name", "insAmount": "Ins. amount", "creditLimitRefName": "Credit limit ref. name", "statuses": {"title": "Status", "active": "Active", "inactive": "Inactive"}, "noInsurance": "The collateral does not have active asset insurance"}, "borrowerDetails": {"role": "Role", "type": "Type", "domesticId": "Unique ID", "about": "About", "firstName": "First name", "middleName": "Middle name", "middleNameOptional": "Middle name", "lastName": "Last name", "birthDate": "Birth date", "gender": "Gender", "companyName": "Company name", "taxNumber": "Tax number", "legalType": "Legal type", "representative": "Representative", "personalIdNumber": "Personal ID number", "roleInCompany": "Role in Company", "communication": "Communication", "mobileNumber": "Mobile number", "landlineNumber": "Landline number", "email": "Email", "address": "Address", "country": "Country", "state": "State", "postalCode": "Postal code", "city": "City", "streetNumberAndName": "Street number and name", "streetNumber": "Street number", "streetName": "Street name", "additionalInfo": "Additional info", "additionalInfoOptional": "Additional info (optional)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "annualRevenue": "Annual revenue", "annualRevenueOptional": "Annual revenue (optional)", "default": "<PERSON><PERSON><PERSON>", "paymentMethods": "Payment methods", "paymentMethod": "<div>PAYMENT</div> <div>METHOD</div>", "maritalStatus": "Marital status", "numberOfChildren": "Number of children", "education": "Education", "sourceOfRepayment": "Source of repayment", "employment": "Employment", "employmentSector": "Employment sector", "employerName": "Employer's name", "employedSince": "Employed since", "employmentType": "Employment type", "incomeCurrency": "Income currency", "monthlyIncome": "Monthly income", "industry": "Industry", "sector": "Sector", "countryOfRegistration": "Country of registration", "mainCountryOfOperation": "Main country of operation", "establishedSince": "Established", "numberOfEmployees": "Number of employees", "addressType": "Address type", "addRepresentative": "Add representative", "deleteRepresentative": "Delete representative", "phones": "Phones", "addPhone": "Add phone", "countryCode": "Code", "phoneNumber": "Phone number", "emails": "Emails", "addEmail": "Add email", "addAddress": "Add address", "deleteAddress": "Delete address", "defaultAddress": "Default address", "delete": "Delete", "main": "Main", "idCard": "ID card", "drivingLicense": "Driving license", "passport": "Passport", "companyRegistration": "Company registration"}, "borrowerPhoneTypes": {"MOBILE": "Mobile", "FIX": "Landline"}, "payerRelationshipType": {"OTHER": "Other", "FAMILY_MEMBER": "Family member", "BUSINESS_PARTNER": "Business partner"}, "borrowerRoles": {"BORROWER": "<PERSON><PERSON><PERSON>", "CO_BORROWER": "Co-borrower", "CO_DEBTOR": "Co-debtor", "GUARANTOR": "Guarant<PERSON>", "ASSET_PROVIDER": "Asset provider", "BENEFICIARY": "Beneficiary", "SUPPORTING_GUARANTOR": "Supporting guarantor", "PAYING_ACCOUNT_OWNER": "Paying account owner", "NON_GUARANTOR_PAYER": "Non guarantor payer", "MAIN_BORROWER": "Main borrower", "ASSET_HOLDER": "Asset provider", "limitOwner": "Limit owner", "limitConsumer": "Limit beneficiary"}, "overview": "Overview", "documents": "Documents", "confirmationDialogMessages": {"DELETE_PARTYTitle": "Remove Party?", "DELETE_PARTYMessage": "Removing loan Party will loose its details from this application.", "DELETE_PORTFOLIOTitle": "Remove Portfolio #{{portfolioNumber}}?", "DELETE_PORTFOLIOMessage": "Removing Portfolio #{{portfolioNumber}} will delete its details from this application.", "DELETE_PORTFOLIO_COLLATERALTitle": "Remove Collateral #{{collateralNumber}}?", "DELETE_PORTFOLIO_COLLATERALMessage": "Removing Collateral #{{collateralNumber}} will delete its details from Portfolio #{{portfolioNumber}}.", "CANCEL_LOANTitle": "Canceling loan creation?", "CANCEL_LOANMessage": "You will lose all the details entered for this loan.", "CANCEL_LIMITTitle": "Discarding product creation?", "CANCEL_LIMITMessage": "You will lose all the details entered for this section.", "CANCEL_LIMIT_APPLICATIONTitle": "Cancelling of Credit Limit?", "CANCEL_LIMIT_APPLICATIONMessage": "You will lose all the details entered for this Limit. This could be done for Credit limits without allocated Portfolios only!", "REPAY_PAYMENTTitle": "Repay installment", "REPAY_PAYMENTMessage": "You are about to mark this installment as paid.", "DELETE_LOANTitle": "Remove loan?", "DELETE_LOANMessage": "Do you want to remove the loan from the Operation?", "DELETE_LOANSTitle": "Remove loans?", "DELETE_LOANSMessage": "Are you sure you want to delete these loans from the Operation?"}, "portfolioAndCollateral": {"title": "Created Portfolios", "createdPortfolio": "Created Portfolio", "currency": "<PERSON><PERSON><PERSON><PERSON>", "approvedAmount": "Approved amount", "purpose": "Purpose", "paymentDay": "Payment day", "contractSigningDate": "Contract signing date", "contractReferenceNumber": "Contract reference number", "description": "Description", "descriptionOptional": "Description (optional)", "portfolioDefinition": "Portfolio definition", "preconditions": "Preconditions", "preconditionsMsg": "A section to report all prerequisites, preconditions, documents and requirements for disbursing loans under specific portfolio are received.", "portfolioMainDetails": "Portfolio main details", "addingPortfolio": "Adding Portfolio", "portfolioDetails": "Portfolio details", "collateral": "Collateral", "collaterals": "Collaterals", "selectExistingCollateral": "Select existing Collateral", "noCollaterals": "There are no available collaterals.", "noCompanies": "There are no available companies.", "collateralType": "Collateral type", "collateralSubType": "Collateral sub type", "collateralAmount": "Collateral amount", "marketValue": "Market value", "collateralOwner": "Add collateral owner", "collateralOwnerInfo": "*Owners need to be defined at Loan related parties step", "collateralDocuments": "Collateral documents", "insurance": "Insurance", "insuranceType": "Insurance type", "insurerNames": "Insurer names", "insuranceAmount": "Insurance amount", "insuranceNumber": "Insurer number", "insuranceDocuments": "Insurance documents", "amount": "Amount", "validFrom": "<PERSON>id from", "validTill": "Valid until", "totalOwnerShip": "Total ownership", "collateralOwnerShip": "Collateral ownership", "pledgeAmount": "Pledge amount", "registrationDate": "Registration date", "expiryDate": "Expiry date", "typeOfDocument": "Type of document", "portfolio": "<span class='me-1 text-compass-60'>Portfolio</span><span class='text-compass-87 font-bold'>{{portfolioReferenceName}}</span>", "collateralPledge": "Collateral pledge", "collateralDefinition": "Collateral definition", "ownerType": "Owner type", "ownerName": "Owner name", "ownershipPercentage": "Ownership percentage", "ownerUniqueId": "Owner unique ID", "collateralOwnership": "Collateral ownership", "uniqueId": "Unique ID", "vatNumber": "VAT number", "taxNumberOfCompany": "Tax number of Company", "legalType": "Legal type", "owner": "Owner", "type": "Type", "portfolioDocuments": "Portfolio documents", "updateLien": "Update Lien", "createCollateral": "Create Collateral", "updateCollateral": "Update Collateral", "or": "or", "notSavedCollaterals": "The following collaterals are still not part of the current portfolio:", "hasNotSavedCollaterals": "(has not saved collaterals)", "noLimits": "There is no limit for this borrower or created limit has 0 available balance.", "referenceName": "Reference name", "beneficiary": {"title": "Beneficiary", "multipleBeneficiary": "Beneficiaries", "name": "Beneficiary name", "uniqueId": "Unique ID", "type": "Beneficiary type", "distributionPercentage": "Distribution percentage", "totalPayoutAmount": "Total payout amount", "payoutAmount": "Payout amount", "totalUnutilizedAmount": "Total unutilized amount", "paymentAmount": "Payment amount", "unutilizedAmount": "Unutilized amount", "paymentAccounts": "Payment accounts", "Account": "Account", "id": "ID", "accountInfo": "Accounts info", "paymentMethod": {"title": "Payment method", "paymentGateway": "Payment gateway", "accountNumber": "Account number", "payout": "Payout"}}, "effectiveDate": "Effective date", "utilizationPeriod": "Utilization period", "endUtilizationDate": "End utilization date", "portfolioDuration": "Portfolio duration", "portfolioMaturityDate": "Portfolio maturity date", "conditionConfirmation": "Confirmation all Condition Precedents are fulfilled", "creditLimit": "<span class='me-1 text-compass-60'>Credit limit</span><span class='text-compass-87 font-bold'>{{limitReferenceName}}</span>", "portfolioAmount": "Portfolio amount", "month": "Month", "months": "Months", "numberOfRepayments": "Number of repayments", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount", "loanMaturityDate": "Loan maturity date", "documents": "Documents", "track": "Track", "tracks": "Tracks", "collateralTypes": {"REAL_ESTATE": "Real estate", "VEHICLES": "Vehicles", "SECURITIES": "Securities", "CASH_OR_CASH_EQUIVALENTS": "Cash or cash equivalents", "INVENTORY": "Inventory", "ACCOUNTS_RECEIVABLE": "Accounts receivable", "EQUIPMENT_AND_MACHINERY": "Equipment and machinery", "ART_AND_COLLECTIBLES": "Art and collectibles", "INTELLECTUAL_PROPERTY": "Intellectual property", "FUTURE_PAYMENTS": "Future payments", "OTHERS": "Others"}, "collateralSubTypes": {"HOUSE": "House", "APARTMENT": "Apartment", "GARDEN_APARTMENT": "Garden apartment", "PRIVATE_HOUSE": "Private house", "PENTHOUSE": "Penthouse", "LAND_PLOT": "Land plot", "PRODUCTION_FACILITY": "Production facility", "WAREHOUSE": "Warehouse", "CAR": "Car", "TRUCK": "Truck", "MOTORCYCLE": "Motorcycle", "STOCKS": "Stocks", "BONDS": "<PERSON><PERSON>", "MUTUAL_FUNDS": "Mutual funds", "SAVINGS_ACCOUNT": "Savings account", "CERTIFICATES_OF_DEPOSIT": "Certificates of deposit", "RETAIL_INVENTORY": "Retail inventory", "RAW_MATERIALS": "Raw materials", "FINISHED_GOODS": "Finished goods", "OUTSTANDING_INVOICES": "Outstanding invoices", "PENDING_PAYMENTS": "Pending payments", "INDUSTRIAL_MACHINERY": "Industrial machinery", "OFFICE_EQUIPMENT": "Office equipment", "CONSTRUCTION_EQUIPMENT": "Construction equipment", "PAINTINGS": "Paintings", "SCULPTURES": "Sculptures", "ANTIQUES": "Antiques", "TRADEMARKS": "Trademarks", "COPYRIGHTS": "Copyrights", "PATENTS": "Patents", "FUTURE_INCOME_STREAMS": "Future income streams", "ROYALTIES": "Royalties", "LEASE_PAYMENTS": "Lease payments", "OTHER": "Other"}, "portfolioTypes": {"COLLATERALIZED": "Secured", "NON_COLLATERALIZED": "Unsecured"}}, "portfolioLoans": {"title": "<span class='me-1 text-compass-60'>Loans attached to Portfolio</span><span class='text-compass-87 font-bold'>{{portfolioName}}</span>", "noLoans": "There are no attached loans to this Portfolio", "tableHeaders": {"id": "ID", "referenceName": "Reference name", "status": "Status", "outstandingAmount": "Outstanding principle", "disbursementDate": "Disbursement date"}}, "trackLoans": {"title": "<span class='me-1 text-compass-60'>Loans attached to Track</span><span class='text-compass-87 font-bold'>{{trackName}}</span>"}, "products": {"title": "Existing portfolios", "portfolio": "Portfolio", "product": "Product", "paymentScheduleType": "Payment schedule type", "numberOfPayments": "Number of payments", "repaymentFrequency": "Repayment frequency", "recurringFrequency": "Recurring frequency", "paymentMethod": "Payment method", "interestType": "Type", "base": "Base", "baseType": "Base type", "baseCode": "Base code", "spreadInterest": "Spread", "payment": "Payment", "interest": "Interest", "regularInterest": "Regular interest", "penaltyInterest": "Penalty interest", "min": "Min", "max": "Max", "interval": "(Interval: {{interval}})", "productDetails": "<span class='me-1 text-compass-60'>Product details for Portfolio</span><span class='text-compass-87 font-bold'>{{portfolioReferenceName}}</span>", "creditType": "Credit type", "paymentSchedule": "Payment schedule", "frequency": "Frequency", "firstPaymentCalcMethod": "First payment calculation method", "calendar": "Calendar", "interestDaysCalcMethod": "Interest days calculation method", "fees": "Fees", "noFees": "There are no fees added", "charge": "Charge", "disabledFee": "Disabled fee", "calcType": "Calculation type", "level": "Level", "currency": "<PERSON><PERSON><PERSON><PERSON>", "minCharge": "Min. Charge Amount", "maxCharge": "Max. Charge Amount", "disabled": "Disabled", "fixedInterest": "Fixed interest", "paymentDate": "Payment date", "grace": "<PERSON>", "graceType": "Grace type", "bulletType": "Bullet type", "period": "Period", "paymentAmount": "Payment amount", "monthlyAmount": "Monthly amount", "disbursement": "Disbursement", "calendarType": "Calendar type", "firstPayment": "First payment", "isGrace": "Is interest capitalization"}, "details": {"title": "Limit details", "general": "General", "referenceName": "Reference name", "referenceNumber": "Reference number", "description": "Description", "contractNumber": "Contract number", "signDate": "Contract signature date", "period": "Period", "effectiveDate": "Effective date", "endDate": "End date", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "limitAmount": "Limit amount", "documents": "Documents", "attachedDocuments": "Attached documents", "uploadDocuments": "Upload documents", "keyIndicators": "Key Indicators", "pti": "PTI", "originalLtv": "Original LTV", "originMarketValueCoverage": "Original market value coverage"}, "identityParty": {"borrowerTitle": "Borrower details", "borrowerNoMatch": "No matching results. Do you want to create a new borrower?", "partyTitle": "Do you want to add deal parties?", "partyAddParty": "Adding deal Party", "partyAddPartyWithCount": "Adding deal Party #{{count}}", "partyAlreadyAddedParty": "Cannot Assign Multiple Roles as a Party in the Same Deal. Please enter a different ID.", "ownerTitle": "Owner details", "ownerNoMatch": "No matching results. Do you want to create a new owner?", "consumerTitle": "Do you want to add beneficiaries?", "consumerAddParty": "Adding Beneficiary", "consumerAddPartyWithCount": "Adding Beneficiary #{{count}}", "consumerAlreadyAddedParty": "Already added in the application.", "applicationAddParty": "Loan related parties", "selectType": "Select type", "uniqueIdentifier": "Unique identifier"}, "borrower": {"title": "Borrowers", "createBorrower": "Create a new <PERSON><PERSON><PERSON>", "updateBorrower": "Update Bo<PERSON><PERSON>", "createParty": "Create a new Party", "updateParty": "Update Party", "createOwner": "Create a new Owner", "updateOwner": "Update Owner", "createConsumer": "Create a new Beneficiary", "updateConsumer": "Update Beneficiary"}, "parties": {"relatedParties": "Loan related parties", "yes": "Yes", "no": "No", "selectType": "Select type", "selectRole": "Select role", "uniqueIdentifier": "Unique identifier", "wantToCreateParty": "No matching results. Do you want to create a new party?", "noParties": "There are no parties added."}, "existingApplication": {"portfolioApplication": "The Portfolio cannot have more than one uncompleted application. Please complete the previous one from here:", "loanApplication": "The Loan cannot have more than one uncompleted application. Please complete the previous one from here:", "borrowerApplication": "The Borrower cannot have more than one uncompleted application. Please complete the previous one from here:", "creditLimitApplication": "The Limit owner cannot have more than one uncompleted application. Please complete the previous one from here", "changeCreditLimitApplication": "The Credit limit cannot have more than one uncompleted application. Please complete the previous one from here:"}, "repaymentTable": {"title": "Repayment table", "repaymentsSimulationTable": "Repayments simulation table", "index": "#", "adjustedDate": "Date", "totalChargeAmount": "Total instalment", "principalCharge": "Principal", "totalInterestAmount": "Interest", "linkageCharge": "Linkage", "totalFeeAmount": "Fees", "endBalance": "Balance", "statusCode": "Status", "componentCharge": " ", "hasNoPayments": "There are no payments.", "fullRepaymentTable": "Full repayment table", "entryType": "Description", "loan": "<span class='text-compass-60'>Loan #{{id}}</span> - <span class='font-bold text-compass-87'>{{referenceName}}</span>", "saveAsCsv": "Save as CSV", "saveAsXls": "Save as <PERSON><PERSON>", "saveAsPdf": "Save as PDF", "borrower": "<PERSON><PERSON><PERSON>", "maturityDate": "Maturity date", "loanAmount": "Loan amount", "paymentsFrequency": "Payments frequency", "numberOfRepayments": "Number of repayments", "totalInterestRate": "Total interest rate", "interestType": "Interest type", "baseRate": "Base rate", "spread": "Spread", "disbursement": "Disbursement", "disbursed": "Disbursed", "paymentStatusCodes": {"1": "Future", "2": "In progress", "3": "Partially paid", "4": "Fully paid", "5": "Partial failure", "6": "Failure", "7": "Canceled"}, "paymentFeeCodes": {"principalCharge": "Principal charge", "interestCharge": "Interest charge", "intermediateInterest": "Intermediate interest", "linkagePrincipalCharge": "Linkage principal charge", "linkageInterestCharge": "Linkage interest charge", "linkageIntermediateInterestCharge": "Linkage intermediate interest charge", "linkageGraceInterestCharge": "Linkage grace interest charge", "linkageGracePrincipalCharge": "Linkage grace principal charge", "gracePrincipalCharge": "<PERSON> principal charge", "graceInterestCharge": "Grace interest charge", "bulletInterestCharge": "Bullet interest charge", "bulletPrincipalCharge": "Bullet principal charge", "earlyRepaymentAccumulatedInterest": "Early repayment accumulated interest"}, "paymentFeeTypes": {"ARREARS_LEGAL_FEES": "Arrears legal fees", "PORTFOLIO_CREATION_FEE": "Portfolio creation fee", "CREATION_TOLL": "Creation toll", "DISBURSEMENT_FEE": "Disbursement fee", "MAINTENANCE_FEE": "Maintenance fee", "REFINANCE_FEE": "Refinance fee", "UNUTILIZED_FEE": "Unutilized fee", "CHANGE_PAYMENT_DAY_OPERATIONAL_FEE": "Change payment day operational fee", "EARLY_REPAYMENT_EARLY_NOTICE_FEE": "Early repayment early notice fee", "EARLY_REPAYMENT_OPERATIONAL_FEE": "Early repayment operational fee", "FREEZE_OPERATIONAL_FEE": "Freeze operational fee", "UNFREEZE_OPERATIONAL_FEE": "Unfreeze operational fee", "CHANGE_LOAN_CONDITIONS_FEE": "Change loan conditions fee", "PRODUCE_LAND_REGISTRY_EXTRACT": "Produce loan registry extract", "MORTGAGE_REGISTRATION": "Mortgage registration", "RETURNED_DEBIT_PAYMENT": "Returned debit payment", "EARLY_REPAYMENT_MAINTENANCE_FEE": "Early repayment maintenance fee", "EARLY_REPAYMENT_CAPITALIZATION_FEE": "Early repayment capitalization fee", "EARLY_REPAYMENT_AVERAGE_INDEX_FEE": "Early repayment average index fee", "LEGAL_EXPENSES": "Legal expenses", "EARLY_REPAYMENT_INTENT_LETTER_FEE": "Early repayment intent letter fee", "LIMIT_CREATION_FEE": "Limit creation fee"}, "regularInterest": "Regular interest", "penaltyInterest": "Penalty interest", "overduePayment": {"title": "Overdue & next installments", "overdueDetails": "Overdue details", "originalInstallment": "Original installment", "originalPrincipal": "Original principal", "originalInterest": "Original interest", "originalCommission": "Original fee", "originalLinkage": "Original linkage", "overdueBalance": "Overdue balance", "overduePrincipal": "Overdue principal", "overdueInterest": "Overdue interest", "overdueCommission": "Overdue fee", "overdueLinkage": "Overdue linkage", "penaltyInterest": "Penalty interest", "penaltyCommission": "Penalty fee"}}, "prepaymentTable": {"title": "Limit&Portfolio fees charges", "entryType": "Payment type", "paymentAdjustedDate": "Payment date (Adjusted)", "completionDate": "Actual charge date", "amount": "Amount", "status": "Status", "types": {"CREATION_FEE": "Creation fee", "CREATION_TOLL": "Creation toll", "DISBURSEMENT_FEE": "Disbursement fee", "MAINTENANCE_FEE": "Maintenance fee", "REFINANCE_FEE": "Refinance fee", "UNUTILIZED_FEE": "Unutilized fee", "PORTFOLIO_CREATION_FEE": "Creation fee"}}, "loanHeader": {"portfolioName": "<span class='me-1 text-compass-60'>Portfolio</span><span class='text-compass-87 font-bold'>{{portfolioName}}</span>", "portfolioAmount": "Portfolio amount", "utilizedAmount": "Utilized amount", "unutilizedAmount": "Unutilized amount", "endUtilizationDate": "End utilization date", "paymentDay": "Payment day", "effectiveDate": "Effective date", "portfolioMaturityDate": "Portfolio maturity date", "loanMaturityDate": "Loan maturity date", "limitDetails": "<span class='me-1 text-compass-60'>Limit</span><span class='text-compass-87 font-bold'>{{limitReferenceName}}</span>", "referenceName": "Reference name", "limitAmount": "Limit amount", "endDate": "End date", "contractSignDate": "Contract sign date", "utilizationPeriod": "Utilization period"}, "disbursement": {"title": "Disbursement", "loan": "Loan", "loans": "Loans", "disbursementData": "Disbursement data", "firstRepaymentData": "First repayment date", "disbursementAmount": "Disbursement amount", "remainingAmount": "Remaining amount for disbursement", "fees": "Fees", "changeRequest": "Disbursement request", "referenceName": "Reference name", "loanEffectiveCreateDate": "Creation date", "interestBaseEffectiveDate": "Interest base date", "firstPaymentDate": "First repayment date", "numberOfPayments": "Number of Repayments", "refreshAnchor": "Refresh anchors", "createAnchor": "Create anchors", "noAnchors": "There is no valid interest base rate for this Creation date."}, "changeDate": {"title": "Change portfolio end utilization date", "setNewDate": "Set new date", "newDate": "New date", "newUtilizationDate": "New end utilization date", "changeRequest": "Change portfolio end utilization date"}, "effectiveDate": {"title": "Set portfolio effective date", "titleSecondary": "Change portfolio effective date", "newEffectiveDate": "New effective date", "fulfilledConditions": "*All conditions Precedents are fulfilled", "notFulfilledConditions": "*All conditions Precedents are not fulfilled", "changeRequest": "Set portfolio effective date", "changeRequestSecondary": "Change portfolio effective date"}, "changeAmount": {"title": "Change portfolio amount", "setNewAmount": "Set new amount", "newAmount": "New amount", "newPortfolioAmount": "New portfolio amount", "changeRequest": "Change portfolio amount"}, "earlyRepayment": {"title": "Early repayment", "titleInfo": "This action will lead to early online repayment in the system. The users may choose to do a full or partial repayment. The system will decrease the principal outstanding amount of the loan and will calculate the relevant fee amounts. Note: The system will expect first the overdue amounts to be repaid.", "partialTitle": "Partial early repayment", "fullTitle": "Full early repayment", "howToRepay": "How do you want to repay your loan?", "amountToRepay": "Amount you want to repay", "selectEarlyRepaymentType": "Select type of early repayment", "typeInfo1": "<span class='font-bold'>Full early repayment</span> will close the loan in the system after submission.", "typeInfo2": "<span class='font-bold'>Partial early repayment</span> will decrease the principal amount, it will do the needed interest recalculations and the loan will stay alive in the system.", "partialRepayment": "Partial repayment", "fullRepayment": "Full repayment", "keepRepaymentAmount": "Keep my repayment amount", "keepRepaymentAmountInfo": "The system will keep the current repayment amount, it will shorten the loan term and update the maturity date accordingly.", "KeepMaturityDate": "Keep my maturity date", "KeepMaturityDateInfo": "The system will keep the current maturity date, it will recalculate the interest and update the next repayment amounts accordingly.", "fullRepaymentPreview": "Full early repayment preview", "calculate": "Calculate", "partialRepaymentPreview": "Partial early repayment preview", "changeRequest": "Change request", "request": "Request", "selectRelatedParty": "Select related party", "requestDate": "Request date", "requestBy": "Request by: ", "effectiveDate": "Effective date", "charge": "Charge", "totalFees": "Total fees: ", "fees": "Fees", "repaymentDistribution": "Repayment distribution"}, "simulatePreview": {"totalRepayment": "Total repayment", "remainingPrincipal": "Remaining principal", "principalToBePaid": "Principal to be paid", "interestToDate": "Interest up to date", "commissionToDate": "Commission up to date", "repaymentFee": "Early repayment fee", "freezeFee": "Freeze fee", "unfreezeFee": "Unfreeze fee", "changeDateFee": "Change date fee", "simulate": "Simulate Repayment table", "partialRepaymentPreview": "Partial early repayment preview", "fullRepaymentPreview": "Full early repayment preview", "freezeRepaymentPreview": "Freeze repayment preview", "unfreezeRepaymentPreview": "Unfreeze repayment preview", "changeDateRepaymentPreview": "Change date repayment preview", "repaymentsSimulation": "Repayments simulation table", "interestAmount": "Interest amount"}, "spreadMethods": {"REDUCE_PAYMENT_NUMBER": "Keep my repayment amount", "REDUCE_MONTHLY_PAYMENT": "Keep my maturity date"}, "creditLimitAllocation": {"title": "Credit limit allocation", "creditLimit": "<span class='me-1 text-compass-60'>Credit limit</span><span class='text-compass-87 font-bold'>{{limitReferenceName}}</span>", "creditLimitAllocationInfo": "Credit limit allocation allows when is created a new Portfolio to utilize amount from Limits (In case of Active limit, the Borrower is a limit beneficiary and there is available amount.", "limitAmount": "Limit amount", "available": "Available", "contractReferenceNumber": "Contract reference number", "referenceName": "Reference name", "creditLimitAmount": "Credit limit amount", "availableAmount": "Available amount", "general": "General", "contractSigningDate": "Contract signing date", "description": "Description", "effectiveDate": "Effective date", "endDate": "End date", "period": "Period", "documents": "Documents", "noLimit": "*No limit allocated"}, "attachedDocuments": {"title": "Attached documents"}, "periods": {"days": "Days", "months": "Months"}, "portfolios": {"title": "Portfolios details", "noPortfolios": "There are no portfolios added.", "noPortfoliosFound": "There are no portfolios found."}, "sortPortfolios": {"search": "Search by Portfolio reference name", "sortBy": "Sort by", "criteria": {"END_UTILIZATION_DATE": "End utilization date", "REFERENCE_NAME": "Reference name", "UNUTILIZED_AMOUNT": "Unutilized amount", "STATUS": "Status"}}, "portfolioProducts": {"title": "<span class='me-1 text-compass-60'>Product details for Portfolio</span><span class='text-compass-87 font-bold'>{{portfolioReferenceName}}</span>", "noProduct": "There is no product added.", "titlePortfolio": "Product details for Portfolio", "titleTrack": "Product details for Track", "mainParams": "Product main parameters"}, "freezePayment": {"title": "Freeze payment", "titleInfo1": "The system will enable forwarding the next payment and do a temporary pause from paying the loan. It will update the maturity date or the recalculate the installment amount accordingly.", "titleInfo2": "In case a loan has an active freezing the system will allow the user to unfreeze it and restore the payments from the next possible date.", "titleInfo3": "<span class='font-bold'>Note:</span> All payments up to Today should be repaid before freezing a payment. Currently the system will allow a maximum pause of 3 monthly repayments.", "freezeReason": "Freeze reason", "freezeDuration": "Freeze duration in Months", "whatToBeFreezed": "What to be frozen", "howToRepaid": "How to be repaired frozen amount", "freezePaymentPreview": "Freeze payment preview", "freezeAmount": "Freeze amount", "fee": "Fee", "noFee": "There is no Freeze fee added", "changeRequestTitle": "Freeze repayment request", "frozenLoan": "This loan is already freezed! If you want to do changes in Freeze first perform Unfreeze operation and apply new Freeze!", "changeRequest": "Change request", "types": {"FULL": "Full (No payments)", "PARTIAL": "Partial (Pay only interest)", "AMOUNT": "Amount (Pay specific amount)"}, "methods": {"NUMBER_OF_MONTHLY_PAYMENTS_INCREASE": "Extend the loan duration with the same number of months", "MONTHLY_PAYMENT_INCREASE": "Increase the monthly repayment amount"}}, "unfreezePayment": {"title": "Unfreeze payment", "unfreezeReason": "Unfreeze reason", "fee": "Fee", "noFee": "There is no Unfreeze fee added", "changeRequestTitle": "Unfreeze repayment request", "changeRequest": "Change request"}, "changePaymentDate": {"title": "Change payment date", "titleInfo": "The system will enable changing the current payment day in the month with a new one. It will recalculate the interest and the repayment amount accordingly.", "selectNewDate": "Select a new payment date", "changeRequestTitle": "Change payment date request", "newChangePaymentDate": "New change payment date", "oldChangePaymentDate": "Old change payment date", "changeRequest": "Change request"}, "loanDetails": {"title": "Loan details", "id": "ID", "referenceName": "Reference name", "disbursementAmount": "Disbursement amount", "outstandingAmount": "Outstanding amount", "status": "Status", "statusReason": "Status reason"}, "withdrawal": {"title": "Application details", "status": "Withdrawal status", "subStatus": "Withdrawal sub status", "date": "Withdrawal date", "amount": "Withdrawal amount", "employeeName": "Employee name", "employeeNumber": "Employee number", "idNumber": "ID number", "department": "Department", "jobTitle": "Job title", "maritalStatus": "Marital status", "population": "Population", "earnedWage": "Earned wage", "available": "Available", "bankAccount": "Bank account", "phoneNumber": "Phone number", "email": "E-mail", "employeeType": "Employee type"}, "calcMethods": {"title": "Calculation method", "ACTUAL_360": "Actual/360", "ACTUAL_ACTUAL": "Actual/Actual", "TYPE_30_360": "30/360"}, "regularInterestTooltip": {"name": "Base name", "rate": "Base rate", "spread": "Spread"}, "portfolioPayer": {"payers": "Payers", "paymentAmountPercentage": "Payment amount percentage", "role": "Role", "payerName": "Payer name", "payersUniqueId": "Payer’s unique ID", "payerRelationshipType": "Payer relationship type", "payerRelationshipComment": "Payer relationship comment", "paymentMethod": "Payment method", "paymentGateway": "Payment gateway", "accountNumber": "Account number", "warningMessage": "This action will update the bank account for this payer across all associated tracks and loans. Do you wish to proceed?", "errorMessage": "The bank account is not valid.", "successMessage": "Successfully updated"}, "generateMonthlyReport": {"referenceName": "Reference Name", "reportDateRange": "Report period", "title": "Generate new Report", "excludedPrimaryActivityIds": {"label": "Primary Activity Identifier(s) to Exclude", "dialogText": "<div class=\"mb-2\">You’re about to generate a report for <b>{{reportMonth}} (selected)</b>.</div><div class=\"mb-2\"><b>Warning:</b> This action should only be taken if you identify an error in the data, <u>and it must be approved by the compliance officer</u>.</div><div class=\"mb-2\">You have requested to exclude the following primary activity identifier(s) from the report: <b>{{excludedPrimaryActivityIds}}</b>.</div><div class=\"mb-2\">This means that the transactions associated with these identifiers will <u>not</u> be included in the report submitted to the AML authority.</div><div class=\"mb-2\">Do you approve?</div>"}, "reportPeriodField": {"periodClosed": "Financial month for report period {{reportPeriod}} is not closed.", "periodNotExists": "Financial month not found for report period {{reportPeriod}}."}}, "overdueTooltip": {"title": "Overdue Portfolios:", "portfolioLabel": "Portfolio", "warning": "The overdue amount is based on today's data and can fluctuate."}, "allocationSimulation": {"title": "Allocation preview", "regularBtn": "Regular", "overdueBtn": "Overdue", "tableColumns": {"priority": "Priority", "level": "Level", "portfolioReferenceName": "Portfolio", "loanExternalName": "Loan", "paymentComponentTypeName": "Component", "originalComponentAmount": "Original amount", "actualComponentAmountPaid": "Allocated amount"}}}