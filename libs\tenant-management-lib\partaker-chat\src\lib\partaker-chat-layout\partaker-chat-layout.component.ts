import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormControl, NonNullableFormBuilder } from '@angular/forms';
import { MatDrawer } from '@angular/material/sidenav';
import { Store } from '@ngrx/store';
import {
  selectDirection,
  selectIsChatIsLoading,
  selectMessages,
  selectSessionId,
  TmLibPartakerChatPageActions,
} from '@tenant-management/lib/state';
import { Directions, PartakerChatMessage } from '@tenant-management/lib/types';
import { Observable, tap } from 'rxjs';

@Component({
  selector: 'tm-partaker-chat-layout',
  templateUrl: './partaker-chat-layout.component.html',
  styleUrls: ['./partaker-chat-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PartakerChatLayoutComponent implements OnInit, AfterViewInit {
  @Input() sessionId = '';
  @Input() vboId = '';
  @Output() closeChat = new EventEmitter<boolean>();

  @ViewChild('chatContent', { static: false })
  chatContent!: ElementRef<HTMLDivElement>;

  @ViewChild('chatDrawer', { static: false })
  chatDrawer!: MatDrawer;

  chatForm = this.fb.group({
    message: [''],
  });
  chatAnimationUrl = '';
  directions = Directions;

  get messageField() {
    return this.chatForm.get('message') as FormControl<string>;
  }

  getChatMessages$ = new Observable<PartakerChatMessage[] | null>();
  getSessionId$ = this.store.select(selectSessionId);
  getDirection$ = this.store.select(selectDirection);
  getIsChatResponseLoading$ = new Observable<boolean>();
  private isLoading = false;

  constructor(
    private fb: NonNullableFormBuilder,
    private store: Store,
    private cd: ChangeDetectorRef,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngAfterViewInit(): void {
    this.chatDrawer.open();
    this.cd.detectChanges();
  }

  private scrollToBottom(behavior?: ScrollBehavior) {
    const chatContentElement = this.chatContent?.nativeElement;
    chatContentElement?.scrollTo({
      top: chatContentElement.scrollHeight,
      behavior,
    });
  }

  ngOnInit(): void {
    this.chatAnimationUrl = `${this.document.baseURI}assets/pictures/partaker-chat/Animation - open.json`;
    this.getChatMessages$ = this.store.select(selectMessages);
    this.getIsChatResponseLoading$ = this.store
      .select(selectIsChatIsLoading)
      .pipe(
        tap((isLoading) => {
          this.isLoading = isLoading;
          setTimeout(() => {
            this.scrollToBottom('smooth');
          }, 0);
        })
      );
    this.getSessionId$.pipe().subscribe((sessionId) => {
      if (sessionId) {
        this.sessionId = sessionId;
      }
    });
  }

  startNewSession() {
    this.sessionId = crypto.randomUUID();
    this.store.dispatch(TmLibPartakerChatPageActions.clearChatState());
    this.store.dispatch(
      TmLibPartakerChatPageActions.setSessionId({
        sessionId: this.sessionId,
      })
    );
  }

  close() {
    this.chatDrawer.close().then(() => {
      this.closeChat.emit();
    });
  }

  submitForm(event?: Event) {
    const keyboardEvent = event as KeyboardEvent;
    if (!this.isLoading) {
      if (keyboardEvent && !keyboardEvent.shiftKey) {
        keyboardEvent.preventDefault();
        this.sendMessage();
      }
      if (!keyboardEvent) {
        this.sendMessage();
      }
    }
    return false;
  }

  trackMessages(index: number, message: PartakerChatMessage) {
    return message?.timestamp;
  }

  private sendMessage() {
    const text = this.messageField.value?.trim();
    if (text) {
      this.store.dispatch(
        TmLibPartakerChatPageActions.sendMessage({
          sessionId: this.sessionId,
          text: { text, timestamp: Date.now(), type: 'user' },
        })
      );
      this.isLoading = true;
      this.messageField.reset();
    }
  }
}
