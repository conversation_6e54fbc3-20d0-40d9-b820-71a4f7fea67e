import {
  ApplicationProductPrepayments,
  BorrowerDetail,
  BorrowerPortfolio,
  BorrowerPortfolioLoanResponse,
  CreditLimitCollateral,
  CreditLimitOwner,
  E2eEarlyRepaymentOverview,
  LifeInsuranceCoverage,
  LoanCreditLimitDetails,
  PortfolioProducts,
  TrackLoanResponse,
} from '@e2e/lib/types';
import { createAction, props } from '@ngrx/store';
import { HttpState, TmDocument } from '@tenant-management/lib/types';
import { CreditLimitDetails } from '../../models/credit-limit-details';
import { CreditLimitLoans } from '../../models/credit-limit-loans';
import { CreditLimitPortfolioResponse } from '../../models/credit-limit-portfolio-response';
import { LetterOfIntent } from '../../models/letter-of-intent';

export const getCreditLimitDetailsSuccess = createAction(
  '[Credit Limit Details API] Get creditLimits Success',
  props<{ creditLimitDetails: CreditLimitDetails }>()
);

export const getCreditLimitDetailsFailure = createAction(
  '[Credit Limit Details API] Get creditLimits Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitOwnerSuccess = createAction(
  '[Credit Limit Owner API] Get credit limit owner Success',
  props<{ creditLimitOwner: CreditLimitOwner }>()
);

export const getCreditLimitOwnerFailure = createAction(
  '[Credit Limit Owner API] Get credit limit owner Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitOwnerInsuranceSuccess = createAction(
  '[Credit Limit Owner API] Get credit limit owner insurance Success',
  props<{ lifeInsuranceCoverage: HttpState<LifeInsuranceCoverage> }>()
);

export const getCreditLimitOwnerInsuranceFailure = createAction(
  '[Credit Limit Owner API] Get credit limit owner insurance Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitConsumersSuccess = createAction(
  '[Credit Limit Owner API] Get credit limits consumer Success',
  props<{ creditLimitConsumers: HttpState<BorrowerDetail[]> }>()
);

export const getCreditLimitConsumersFailure = createAction(
  '[Credit Limit Owner API] Get credit limits consumer Failure',
  props<{ error: unknown }>()
);

export const sendLimitDetailsFilestackDocumentSuccess = createAction(
  '[Credit Limit Details PAGE] Send limit details filestack document Success',
  props<{ uploadedDocument: TmDocument }>()
);

export const sendLimitDetailsFilestackDocumentFailure = createAction(
  '[Credit Limit Details PAGE] Send limit details filestack document Failure',
  props<{ error: unknown }>()
);

export const sendLimitDetailsDocumentSuccess = createAction(
  '[Credit Limit upload document] Send limit details document Success',
  props<{ creditLimitDetails: CreditLimitDetails }>()
);

export const sendLimitDetailsDocumentFailure = createAction(
  '[Credit Limit upload document] Send limit details document Failure',
  props<{ error: unknown }>()
);

export const sendChangeAmountSuccess = createAction(
  '[Credit Limit Details API] Send change amount Success'
);

export const sendChangeAmountFailure = createAction(
  '[Credit Limit Details API] Send change amount Failure',
  props<{ error: unknown }>()
);

export const cancelCreditLimitSuccess = createAction(
  '[Credit Limit Details API] Cancel credit Limits Success'
);

export const cancelCreditLimitFailure = createAction(
  '[Credit Limit Details API] Cancel credit Limits Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitPortfoliosSuccess = createAction(
  '[Credit Limit Owner API] Get credit limits Success',
  props<{ creditLimitPortfolios: HttpState<CreditLimitPortfolioResponse> }>()
);

export const getCreditLimitPortfoliosFailure = createAction(
  '[Credit Limit Owner API] Get credit limits Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitPortfolioDetailsSuccess = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio details Success',
  props<{ portfolioIndex: number; portfolioDetails: BorrowerPortfolio }>()
);

export const getCreditLimitPortfolioDetailsFailure = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio details Failure',
  props<{ error: unknown }>()
);

export const getPortfolioLimitSuccess = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio limit Success',
  props<{ portfolioIndex: number; limit: LoanCreditLimitDetails }>()
);

export const getPortfolioLimitFailure = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio limit Failure',
  props<{ error: unknown }>()
);

export const getBorrowerPortfolioLoansSuccess = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio loans Success',
  props<{ portfolioLoans: BorrowerPortfolioLoanResponse }>()
);

export const getBorrowerPortfolioLoansFailure = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio loans Failure',
  props<{ error: unknown }>()
);

export const getTrackLoansSuccess = createAction(
  '[Credit Limit portfolios API] Get track loans Success',
  props<{
    trackLoans: TrackLoanResponse;
  }>()
);

export const getTrackLoansFailure = createAction(
  '[Credit Limit portfolios API] Get track loans Failure',
  props<{ error: unknown }>()
);

export const getBorrowerPortfolioProductsSuccess = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio products Success',
  props<{
    portfolioProducts: PortfolioProducts;
  }>()
);

export const getBorrowerPortfolioProductsFailure = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio products Failure',
  props<{ error: unknown }>()
);

export const sendDisbursementSuccess = createAction(
  '[Credit Limit portfolios disbursement API] Send disbursement Success'
);

export const sendDisbursementFailure = createAction(
  '[Credit Limit portfolios disbursement API] Send disbursement Failure',
  props<{ error: unknown }>()
);
export const sendChangeDateSuccess = createAction(
  '[Credit Limit portfolios change date API] Send change date Success'
);

export const sendChangeDateFailure = createAction(
  '[Credit Limit portfolios change date API] Send change date Failure',
  props<{ error: unknown }>()
);

export const sendEffectiveDateSuccess = createAction(
  '[Credit Limit portfolios effective date PAGE] Send effective date Success'
);

export const sendEffectiveDateFailure = createAction(
  '[Credit Limit portfolios effective date PAGE] Send effective date Failure',
  props<{ error: unknown }>()
);

export const sendChangeEffectiveDateSuccess = createAction(
  '[Credit Limit Details portfolios change effective date PAGE] Send change effective date Success'
);

export const sendChangeEffectiveDateFailure = createAction(
  '[Credit Limit Details portfolios change effective date PAGE] Send change effective date Failure',
  props<{ error: unknown }>()
);

export const sendChangePortfolioAmountSuccess = createAction(
  '[Credit Limit portfolios change amount PAGE] Send change amount Success'
);

export const sendChangePortfolioAmountFailure = createAction(
  '[Credit Limit portfolios change amount PAGE] Send change amount Failure',
  props<{ error: unknown }>()
);

export const getBorrowerPortfolioPrepaymentsProductsSuccess = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio prepayments products Success',
  props<{
    prepayments: ApplicationProductPrepayments[];
  }>()
);

export const getBorrowerPortfolioPrepaymentsProductsFailure = createAction(
  '[Credit Limit portfolios API] Get credit limits portfolio prepayments products Failure',
  props<{ error: unknown }>()
);

export const getCollateralsSuccess = createAction(
  '[Credit Limit collaterals PAGE] Get collaterals Success',
  props<{ creditLimitCollaterals: HttpState<CreditLimitCollateral[]> }>()
);

export const getCollateralsFailure = createAction(
  '[Credit Limit collaterals PAGE] Get collaterals Failure',
  props<{ error: unknown }>()
);

export const createNewInsuranceSuccess = createAction(
  '[Credit Limit collaterals PAGE] Create new insurance Success'
);

export const createNewInsuranceFailure = createAction(
  '[Credit Limit collaterals PAGE] Create new insurance Failure',
  props<{ error: unknown }>()
);

export const getFeesSuccess = createAction(
  '[Credit Limit PAGE] Get fees Success',
  props<{
    fees: any;
  }>()
);

export const getFeesFailure = createAction(
  '[Credit Limit PAGE] Get fees Failure',
  props<{ error: unknown }>()
);

export const getOverviewSuccess = createAction(
  '[Credit Limit PAGE] Get getOverview Success',
  props<{
    overview: E2eEarlyRepaymentOverview;
  }>()
);

export const getOverviewFailure = createAction(
  '[Credit Limit PAGE] Get getOverview Failure',
  props<{ error: unknown }>()
);

export const getOverdueSuccess = createAction(
  '[Credit Limit Details PAGE] Get Overdue Success',
  props<{
    creditLimitOverdue: any;
  }>()
);

export const getOverdueFailure = createAction(
  '[Credit Limit Details PAGE] Get Overdue Failure',
  props<{ error: unknown }>()
);

export const sendEarlyRepaymentRequestSuccess = createAction(
  '[Credit Limit Details PAGE] Send Early Repayment Request Success'
);

export const sendEarlyRepaymentRequestFailure = createAction(
  '[Credit Limit Details PAGE] Send Early Repayment Request Failure',
  props<{ error: unknown }>()
);

export const submitCreditLimitEarlyRepaymentSuccess = createAction(
  '[Credit Limit Details PAGE] Submit Credit Limit Early Repayment Request Success'
);

export const submitCreditLimitEarlyRepaymentFailure = createAction(
  '[Credit Limit Details PAGE] Submit Credit Limit Early Repayment Request Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitSimulatedRepaymentsSuccess = createAction(
  '[Credit Limit Details PAGE] Get Credit Limit Early Repayment Simulated Repayments Success',
  props<{
    loanSimulatedRepayment: any;
  }>()
);

export const getCreditLimitSimulatedRepaymentsFailure = createAction(
  '[Credit Limit Details PAGE] Get Credit Limit Early Repayment Simulated Repayments Failure',
  props<{ error: unknown }>()
);

export const getLoanDebtRepaymentsSuccess = createAction(
  '[Credit Limit Details PAGE] Get Loan Debt Simulated Repayments Success',
  props<{
    loanSimulatedRepayment: any;
  }>()
);

export const getLoanDebtRepaymentsFailure = createAction(
  '[Credit Limit Details PAGE] Get Loan Debt Simulated Repayments Failure',
  props<{ error: unknown }>()
);

export const getRequestSimulateSuccess = createAction(
  '[Credit Limit Details PAGE] Get Request Simulate Success',
  props<{
    earlyRepaymentSimulate: any;
  }>()
);

export const getRequestSimulateFailure = createAction(
  '[Credit Limit Details PAGE] Get Request Simulate Failure',
  props<{ error: unknown }>()
);

export const getCreditLimitLoansSuccess = createAction(
  '[Credit Limit Loans PAGE] Get credit limit loans Success',
  props<{
    creditLimitLoans: HttpState<CreditLimitLoans>;
  }>()
);

export const getCreditLimitLoansFailure = createAction(
  '[Credit Limit Loans PAGE] Get credit limit loans Failure',
  props<{ error: unknown }>()
);

export const generateLetterOfIntentSuccess = createAction(
  '[Credit Limit Details PAGE] Generate letter of intent Success',
  props<{ letterOfIntent: LetterOfIntent }>()
);

export const generateLetterOfIntentFailure = createAction(
  '[Credit Limit Details PAGE] Generate letter of intent Failure',
  props<{ error: unknown }>()
);
