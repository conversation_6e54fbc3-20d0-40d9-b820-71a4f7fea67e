import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  NonNullableFormBuilder,
  Validators,
} from '@angular/forms';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { Event, NavigationStart, Router } from '@angular/router';
import {
  E2eLoanPageActions,
  selectLoanExistingApplications,
} from '@e2e/lib/state';
import {
  BorrowerTypes,
  CreditLimitEarlyRepayment,
  CreditLimitOwner,
  E2eEarlyRepaymentOverview,
  EarlyRepaymentTypes,
  FeeTypes,
  LoanEarlyRepaymentFee,
  LoanEarlyRepaymentFeeSimulate,
  LoanExistingApplication,
  LoanSpreadMethods,
  onGetCurrencySymbol,
  Payer,
} from '@e2e/lib/types';
import { Store } from '@ngrx/store';
import {
  selectAllowedOnlyDebtRepayment,
  selectCurrencies,
} from '@tenant-management/lib/state';
import { ControlsOf } from '@tenant-management/lib/types';
import { isEmpty, isEqual } from 'lodash';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  merge,
  Observable,
  of,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { distinctUntilChanged, shareReplay } from 'rxjs/operators';
import {
  selectCreditLimitId,
  selectCreditLimitOwner,
  selectEarlyRepaymentForm,
  selectFees,
  selectLoanIdentityParties,
  selectOverview,
  selectPayers,
} from '../../+state';
import { CreditLimitDetailsPageActions } from '../../+state/actions';
import { calculateFixedFees, normalizePerecentageRate } from '../../utils';

@Component({
  selector: 'e2e-credit-limit-early-repayment',
  templateUrl: './credit-limit-early-repayment.component.html',
  styleUrls: ['./credit-limit-early-repayment.component.scss'],
})
export class CreditLimitEarlyRepaymentComponent implements OnInit, OnDestroy {
  repaymentTypes = EarlyRepaymentTypes;
  spreadMethods = LoanSpreadMethods;
  borrowerTypes = BorrowerTypes;

  todayDate = new Date();
  fees!: LoanEarlyRepaymentFee[];
  amountToRepay: number | undefined;
  fullAmountProp = 0;
  totalDebtAmount = 0;
  totalAmountFullRepayment = 0;
  totalFixedFeesAmount = 0;
  portfoliosIds: any;
  allowedOnlyDebtRepayment = false;

  getCreditLimitFees$ = new Observable<any>();
  getCurrency$ = new Observable<any>();
  getEarlyRepaymentForm$ = new Observable<CreditLimitEarlyRepayment>();
  selectCreditLimitIdentityParties$ = new Observable<CreditLimitOwner>();
  selectPayers$ = new Observable<any>();
  getOverview$ = new Observable<E2eEarlyRepaymentOverview | null>();
  isEarlyRepaymentSimulate = false;
  onFormChange$ = new Observable<unknown>();

  getAllowedOnlyDebtRepayment$ = new Observable<boolean>();
  getCreditLimitId$ = new Observable<string>();
  getExistingApplications$ = new Observable<LoanExistingApplication[]>();
  repaymentAndFeesVisibleCondition$ = new BehaviorSubject(true);
  repaymentAndFeesVisible$ = this.repaymentAndFeesVisibleCondition$
    .asObservable()
    .pipe(
      tap((status) => {
        this.toggleDistributionFormElements(status);
      })
    );

  earlyRepaymentForm = this.fb.group<ControlsOf<CreditLimitEarlyRepayment>>({
    repaymentType: this.fb.control<EarlyRepaymentTypes | null>(
      null,
      Validators.required
    ),
    // repaymentType: this.fb.control<EarlyRepaymentTypes | null>(
    //   { value: EarlyRepaymentTypes.Partial, disabled: true },
    //   Validators.required
    // ),
    relatedParty: this.fb.control('', Validators.required),
    payer: this.fb.control<Payer | null>(
      { value: null, disabled: true },
      Validators.required
    ),
    requestDate: this.fb.control<Date | null>(null, Validators.required),
    effectiveDate: this.fb.control<Date | null>(null, Validators.required),

    amountToRepay: this.fb.control(0, Validators.required),
    repaymentDistribution: this.fb.control(
      '',
      // LoanSpreadMethods.ReducePaymentNumber,
      Validators.required
    ),
    operationalFee: this.fb.control<boolean>(true),
    earlyNoticeFee: this.fb.control<boolean>(true),
    capitalizationFee: this.fb.control<boolean>(true),
    fees: this.fb.array<FormGroup>([]),
  });

  get repaymentTypeField() {
    return this.earlyRepaymentForm.get(
      'repaymentType'
    ) as unknown as FormControl<EarlyRepaymentTypes>;
  }

  get repaymentDistributionField() {
    return this.earlyRepaymentForm.get(
      'repaymentDistribution'
    ) as unknown as FormControl<any>;
  }
  get repaymentAmountToRepayField() {
    return this.earlyRepaymentForm.get(
      'amountToRepay'
    ) as unknown as FormControl<any>;
  }
  get effectiveDateField() {
    return this.earlyRepaymentForm.get(
      'effectiveDate'
    ) as FormControl<Date | null>;
  }
  get payerField() {
    return this.earlyRepaymentForm.get('payer') as FormControl<Payer | null>;
  }

  private destroy$ = new Subject<void>();

  constructor(
    private store: Store,
    private fb: NonNullableFormBuilder,
    private router: Router
  ) {}

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit(): void {
    this.getCreditLimitId$ = this.store.select(selectCreditLimitId).pipe(
      tap((limitId) => {
        if (limitId) {
          this.store.dispatch(
            E2eLoanPageActions.getExistingEarlyRepaymentApplications({
              creditLimitId: limitId,
            })
          );
        }
      })
    );

    this.getExistingApplications$ = this.store
      .select(selectLoanExistingApplications)
      .pipe(
        tap((loanExistingApplications) => {
          if (loanExistingApplications) {
            if (loanExistingApplications.length) {
              this.earlyRepaymentForm.disable();
            } else {
              this.earlyRepaymentForm.enable();
            }
          }
        })
      );

    this.router.events
      .pipe(
        filter(
          (event: Event): event is NavigationStart =>
            event instanceof NavigationStart
        ),
        takeUntil(this.destroy$)
      )
      .subscribe((event) => {
        this.isEarlyRepaymentSimulate = this.router.url.includes('/simulate');
        if (!event.url.includes('simulate')) {
          this.store.dispatch(
            CreditLimitDetailsPageActions.resetCreditLimitEarlyRepaymentState()
          );
        }
      });
    this.store.dispatch(CreditLimitDetailsPageActions.getFees());

    this.onFormChange$ = this.earlyRepaymentForm.valueChanges.pipe(
      distinctUntilChanged(isEqual),
      startWith(this.earlyRepaymentForm.value),
      tap(() => {
        this.store.dispatch(
          CreditLimitDetailsPageActions.setIsFormValid({
            isFormValid: this.earlyRepaymentForm.valid,
          })
        );
        if (this.earlyRepaymentForm.valid) {
          const earlyRepaymentForm =
            this.earlyRepaymentForm.getRawValue() as CreditLimitEarlyRepayment;
          this.store.dispatch(
            CreditLimitDetailsPageActions.setEarlyRepaymentForm({
              earlyRepaymentForm,
            })
          );
        }
        this.getAmount();
      }),
      takeUntil(this.destroy$)
    );

    // Listen for change in amount field and the effective date in order to show/shide fees and distribution form fields
    merge(
      this.store.select(selectOverview),
      this.earlyRepaymentForm.controls['amountToRepay'].valueChanges
    )
      .pipe(
        filter(
          (data) =>
            !!data &&
            data !== null &&
            (typeof data === 'number' ||
              (typeof data === 'object' && Object.values(data).length > 0))
        ),
        takeUntil(this.destroy$)
      )
      .subscribe((data) => {
        this.repaymentAndFeesVisibleCheck();
      });

    this.selectPayers$ = combineLatest([
      this.store.select(selectPayers),
      this.store.select(selectOverview),
    ]).pipe(
      filter((data) => !!data[1]),
      switchMap((data) => {
        if (!data[0][0]?.identityPartyName) {
          return this.store.select(selectCreditLimitOwner).pipe(
            switchMap((owner) => {
              let identityPartyName = '';
              let identityPartyId = '';
              if (owner.loanIdentityParties[0].identityParty.person) {
                identityPartyName = `${owner.loanIdentityParties[0].identityParty.person.name} ${owner.loanIdentityParties[0].identityParty.person.familyName}`;
              }
              if (owner.loanIdentityParties[0].identityParty.company) {
                identityPartyName = `${owner.loanIdentityParties[0].identityParty.company.name}`;
                identityPartyId = `${owner.loanIdentityParties[0].identityParty.id}`;
              }
              return of([
                {
                  ...data[0][0],
                  identityPartyId,
                  identityPartyName,
                },
              ]);
            })
          );
        } else {
          return of(data[0]);
        }
      }),
      tap(() => {
        this.earlyRepaymentForm.controls['payer'].setValue(null);
      })
    );

    this.getCurrency$ = this.store.select(selectCurrencies);
    this.getOverview$ = this.store.select(selectOverview).pipe(
      tap((overview) => {
        if (overview && Object.keys(overview).length) {
          this.fullAmountProp = overview.totalAmountFullRepayment;
          this.totalDebtAmount = overview.totalDebtAmount;
          this.payerField.enable({ emitEvent: false });
          this.payerField.updateValueAndValidity({ emitEvent: false });

          // this.totalAmountFullRepayment = overview.totalAmountFullRepayment;

          this.portfoliosIds = overview.portfolios?.map((item) => item.id);

          // this.repaymentAmountToRepayField.reset();
          if (this.allowedOnlyDebtRepayment) {
            this.repaymentAmountToRepayField.setValidators([
              Validators.required,
              Validators.min(1),
              Validators.max(this.totalDebtAmount),
            ]);
            this.repaymentAmountToRepayField.updateValueAndValidity({
              emitEvent: false,
            });
            this.repaymentAndFeesVisibleCheck();
          }

          // this.selectPayers$ = this.store.select(selectPayers);

          if (this.repaymentTypeField.value === this.repaymentTypes.Full) {
            if (overview?.portfolios?.length) {
              overview.portfolios.forEach((portfolio) => {
                this.store.dispatch(
                  CreditLimitDetailsPageActions.selectPortfolio({ portfolio })
                );

                this.store.dispatch(
                  CreditLimitDetailsPageActions.changePortfolioMode({
                    portfolioId: portfolio.id,
                    mode: this.repaymentTypes.Full,
                  })
                );
              });
            }
            this.getAmount();
          }
        }
      })
    );

    this.getEarlyRepaymentForm$ = this.store
      .select(selectEarlyRepaymentForm)
      .pipe(
        take(1),
        tap((earlyRepaymentForm) => {
          if (earlyRepaymentForm) {
            this.earlyRepaymentForm.patchValue(earlyRepaymentForm, {
              emitEvent: false,
            });
            this.repopulateFees(earlyRepaymentForm.fees);
            this.onChangeRepaymentType();
          }
        })
      );

    this.selectCreditLimitIdentityParties$ = this.store.select(
      selectLoanIdentityParties
    );

    this.getCreditLimitFees$ = this.store.select(selectFees).pipe(
      withLatestFrom(this.store.select(selectEarlyRepaymentForm)),
      switchMap(([fees, form]) => {
        if (fees && isEmpty(form)) {
          this.addFees(fees);
        }
        return of(fees);
      })
    );

    const activeFees = this.getActiveFees();
    activeFees.forEach((fee) => {
      fee.percentageRate = fee.percentageRate ? +fee.percentageRate / 100 : '';
    });

    this.getAllowedOnlyDebtRepayment$ = this.store
      .select(selectAllowedOnlyDebtRepayment)
      .pipe(
        tap((allowedOnlyDebtRepayment) => {
          if (allowedOnlyDebtRepayment) {
            this.allowedOnlyDebtRepayment = true;
            this.repaymentTypeField?.setValue(EarlyRepaymentTypes.Partial);
            this.repaymentTypeField?.disable();

            this.repaymentDistributionField?.setValue(
              LoanSpreadMethods.ReducePaymentNumber
            );
            this.repaymentDistributionField?.disable();
          }
        }),
        shareReplay({
          bufferSize: 1,
          refCount: true,
        })
      );
  }

  get feesFormArray(): FormArray {
    return this.earlyRepaymentForm.get('fees') as FormArray;
  }

  changedEffectiveDate(event: MatDatepickerInputEvent<Date>) {
    const fees = this.getActiveFees().map((fee) => {
      const percentageRate = normalizePerecentageRate(fee.percentageRate);
      return {
        ...fee,
        percentageRate,
      };
    });
    this.store.dispatch(
      CreditLimitDetailsPageActions.getOverview({
        effectiveDate: event.value as Date,
        fees,
      })
    );
  }

  changePercentageFee(feeType: string, event?: MatSlideToggleChange) {
    const effectiveDate =
      this.earlyRepaymentForm.controls['effectiveDate'].value;
    if (
      feeType === FeeTypes.EarlyRepaymentCapitalizationFee ||
      feeType === FeeTypes.EarlyRepaymentEarlyNoticeFee
    ) {
      const activeFees = this.getActiveFees().map((fee) => ({
        ...fee,
        percentageRate: fee.percentageRate ? +fee.percentageRate / 100 : '',
      }));
      if (effectiveDate !== null && event?.checked) {
        this.store.dispatch(
          CreditLimitDetailsPageActions.getOverview({
            effectiveDate: effectiveDate,
            fees: activeFees,
          })
        );
      }
    }
  }

  addFees(fees: LoanEarlyRepaymentFee[]) {
    const feesFormArray = this.earlyRepaymentForm.get(
      'fees'
    ) as unknown as FormArray<
      FormGroup<Partial<ControlsOf<LoanEarlyRepaymentFeeSimulate>>>
    >;
    feesFormArray.clear();
    fees.forEach((fee) => {
      const hasPercentage =
        fee.percentage && fee.percentage.percentageRate !== undefined;
      const hasFixed = fee.fixed && fee.fixed.fixedRate !== undefined;

      const feeFormGroup = this.fb.group<
        Partial<ControlsOf<LoanEarlyRepaymentFeeSimulate>>
      >({
        type: this.fb.control(fee.feeType),
        spreadMethod: this.fb.control(fee.spreadMethod),
        calcType: this.fb.control({ value: fee.calcType, disabled: true }),
        minAmount: this.fb.control(fee.minFee),
        maxAmount: this.fb.control(fee.maxFee),
        currency: this.fb.control(fee.currency),
        // Display fees percentage rate multiply by 100
        percentageRate: this.fb.control(
          hasPercentage ? fee.percentage?.percentageRate * 100 : ''
        ),
        fixedAmount: this.fb.control(hasFixed ? fee.fixed?.fixedRate : ''),
        // calculationBase: this.fb.control(
        //   hasPercentage ? 'Percentage' : hasFixed ? 'FIXED' : 'NONE'
        // ),
        // calculationBase: this.fb.control(
        //   'REPAID_PRINCIPAL_AMOUNT'
        // ),
        calculationBase: this.fb.control(
          hasPercentage ? 'REPAID_PRINCIPAL_AMOUNT' : hasFixed ? null : null
        ),
        isActive: this.fb.control(fee.isActive || false),
        name: this.fb.control(fee.name || ''),
        id: this.fb.control(fee.id.id || ''),
      });

      feesFormArray.push(feeFormGroup);
    });
  }

  onChangeRepaymentType() {
    if (this.repaymentTypeField.value === this.repaymentTypes.Partial) {
      this.repaymentAmountToRepayField.addValidators([
        Validators.required,
        Validators.min(1),
      ]);
      this.repaymentAmountToRepayField.updateValueAndValidity({
        emitEvent: false,
      });
      // this.repaymentAmountToRepayField.addValidators(
      //   Validators.max(this.fullAmountProp)
      // );

      // === Select all portfolios in FULL mode ===
      this.getOverview$.pipe(take(1)).subscribe((overview) => {
        if (overview?.portfolios?.length) {
          overview.portfolios.forEach((portfolio) => {
            this.store.dispatch(
              CreditLimitDetailsPageActions.deselectPortfolio({
                portfolioId: portfolio.id,
              })
            );

            this.store.dispatch(
              CreditLimitDetailsPageActions.changePortfolioMode({
                portfolioId: portfolio.id,
                mode: this.repaymentTypes.Partial,
              })
            );
          });
        }
      });
    } else {
      this.repaymentAmountToRepayField.clearValidators();
      this.repaymentAmountToRepayField.reset();

      // === Select all portfolios in FULL mode ===
      this.getOverview$.pipe(take(1)).subscribe((overview) => {
        if (overview?.portfolios?.length) {
          overview.portfolios.forEach((portfolio) => {
            this.store.dispatch(
              CreditLimitDetailsPageActions.selectPortfolio({ portfolio })
            );

            this.store.dispatch(
              CreditLimitDetailsPageActions.changePortfolioMode({
                portfolioId: portfolio.id,
                mode: this.repaymentTypes.Full,
              })
            );
          });
        }
      });
    }

    this.getAmount();
  }

  getAmount() {
    const repaymentType = this.repaymentTypeField.value;
    this.totalFixedFeesAmount = calculateFixedFees(
      this.earlyRepaymentForm.controls['fees'].value
    );
    if (repaymentType === this.repaymentTypes.Full) {
      this.amountToRepay =
        this.fullAmountProp + this.totalDebtAmount + this.totalFixedFeesAmount;
    } else {
      this.amountToRepay = this.repaymentAmountToRepayField.value;
    }

    // TODO amountToRepay
    this.store.dispatch(
      CreditLimitDetailsPageActions.setAmountToRepay({
        amount: this.repaymentAmountToRepayField.value,
      })
    );
  }

  onGetCurrencySymbol(currency: string) {
    return onGetCurrencySymbol(currency);
  }

  getActiveFees(): LoanEarlyRepaymentFeeSimulate[] {
    const feesFormArray = this.earlyRepaymentForm.get('fees') as FormArray;
    return feesFormArray.controls
      .filter((feeGroup) => feeGroup.get('isActive')?.value)
      .map((feeGroup) => feeGroup.getRawValue());
  }

  sendEarlyRepaymentRequest() {
    const earlyRepaymentForm =
      this.earlyRepaymentForm.getRawValue() as CreditLimitEarlyRepayment;

    const activeFees = this.getActiveFees();
    activeFees.forEach((fee) => {
      fee.percentageRate = fee.percentageRate ? +fee.percentageRate / 100 : '';
    });

    this.store.dispatch(
      CreditLimitDetailsPageActions.sendEarlyRepaymentRequest({
        portfolioIds: this.portfoliosIds,
        earlyRepaymentForm,
        fees: activeFees,
      })
    );
  }

  private repaymentAndFeesVisibleCheck() {
    const amountToRepayControl =
      this.earlyRepaymentForm.controls['amountToRepay'];
    const amountToRepay = amountToRepayControl.value || 0;
    if (
      !this.allowedOnlyDebtRepayment &&
      !this.allowedOnlyDebtRepayment &&
      amountToRepay <= this.totalDebtAmount &&
      !amountToRepayControl.pristine
    ) {
      this.repaymentAndFeesVisibleCondition$.next(false);
    } else {
      this.repaymentAndFeesVisibleCondition$.next(true);
    }
  }

  private toggleDistributionFormElements(status: boolean) {
    //TODO: According to this logic, disabling the fees is not possible.
    const feesInputs = (this.earlyRepaymentForm.controls['fees'] as FormArray)
      .controls;
    if (status) {
      feesInputs.forEach((formGroup) => {
        formGroup.get('isActive')?.patchValue(true, { emitEvent: false });
      });
      this.earlyRepaymentForm.controls['repaymentDistribution'].setValidators([
        Validators.required,
      ]);
      this.earlyRepaymentForm.controls[
        'repaymentDistribution'
      ].updateValueAndValidity({ emitEvent: false });
    } else {
      feesInputs.forEach((formGroup) => {
        formGroup.get('isActive')?.patchValue(false, { emitEvent: false });
      });
      this.earlyRepaymentForm.controls[
        'repaymentDistribution'
      ].clearValidators();
      this.earlyRepaymentForm.controls['repaymentDistribution'].patchValue(
        null,
        { emitEvent: false }
      );
      this.earlyRepaymentForm.controls[
        'repaymentDistribution'
      ].updateValueAndValidity({ emitEvent: false });
    }
  }

  private repopulateFees(fees: LoanEarlyRepaymentFeeSimulate[]) {
    const feesFormArray = this.earlyRepaymentForm.get(
      'fees'
    ) as unknown as FormArray<
      FormGroup<Partial<ControlsOf<LoanEarlyRepaymentFeeSimulate>>>
    >;
    feesFormArray.clear();
    fees?.forEach((fee) => {
      const hasPercentage = fee.type === FeeTypes.EarlyRepaymentEarlyNoticeFee;
      const hasFixed = fee.type === FeeTypes.EarlyRepaymentOperationalFee;
      const feeFormGroup = this.fb.group<
        Partial<ControlsOf<LoanEarlyRepaymentFeeSimulate>>
      >({
        type: this.fb.control(fee.type),
        spreadMethod: this.fb.control(fee.spreadMethod),
        calcType: this.fb.control({ value: fee.calcType, disabled: true }),
        minAmount: this.fb.control(fee.minAmount),
        maxAmount: this.fb.control(fee.maxAmount),
        currency: this.fb.control(fee.currency),
        percentageRate: this.fb.control(
          hasPercentage ? fee.percentageRate : ''
        ),
        fixedAmount: this.fb.control(hasFixed ? fee.fixedAmount : ''),
        calculationBase: this.fb.control(
          hasPercentage ? 'REPAID_PRINCIPAL_AMOUNT' : hasFixed ? null : null
        ),
        isActive: this.fb.control(fee.isActive || false),
        name: this.fb.control(fee.name || ''),
        id: this.fb.control(fee.id || ''),
      });

      feesFormArray.push(feeFormGroup);
    });
  }
}
