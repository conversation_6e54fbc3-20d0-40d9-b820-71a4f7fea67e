import { createAction, props } from '@ngrx/store';
import { PartakerChatMessage } from '@tenant-management/lib/types';

export const sendMessage = createAction(
  '[Partaker Chat PAGE] Send chat message',
  props<{ sessionId: string; text: PartakerChatMessage }>()
);

export const messageSent = createAction(
  '[Partaker Chat PAGE] Chat message sent'
);

export const clearChatState = createAction(
  '[Partaker Chat PAGE] Clear chat state'
);
export const setSessionId = createAction(
  '[Partaker Chat PAGE] Set chat session id',
  props<{ sessionId: string }>()
);
