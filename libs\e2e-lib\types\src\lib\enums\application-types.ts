export enum ApplicationTypes {
  LoanApplication = 'LOAN_APPLICATION',
  Disbursement = 'DISBURSEMENT',
  SetPortfolioEffectiveDate = 'SET_PORTFOLIO_EFFECTIVE_DATE',
  ChangePortfolioEffectiveDate = 'CHANGE_PORTFOLIO_EFFECTIVE_DATE',
  ChangePortfolioEndUtilizationDate = 'CHANGE_PORTFOLIO_END_UTILIZATION_DATE',
  ChangePortfolioAmount = 'CHANGE_PORTFOLIO_AMOUNT',
  FreezeLoan = 'FREEZE_LOAN',
  UnfreezeLoan = 'UNFREEZE_LOAN',
  PartialEarlyRepayment = 'PARTIAL_EARLY_REPAYMENT',
  FullEarlyRepayment = 'FULL_EARLY_REPAYMENT',
  PaymentMethod = 'PAYMENT_METHOD',
  ChangePaymentDay = 'CHANGE_PAYMENT_DAY',
  NewCreditLimit = 'NEW_CREDIT_LIMIT',
  ChangeCreditLimitAmount = 'CHANGE_CREDIT_LIMIT_AMOUNT',
  EwaWithdrawal = 'EWA_WITHDRAWAL',
  MortgageNewDeal = 'MORTGAGE_NEW_DEAL',
  MortgageDisbursement = 'MORTGAGE_DISBURSEMENT',
  CreditLimitEarlyRepayment = 'CREDIT_LIMIT_EARLY_REPAYMENT',
  LetterOfIntent = 'CREDIT_LIMIT_LETTER_OF_INTENT',
}
