import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import {
  selectDateFormat,
  TenantManagementLibPageActions,
} from '@tenant-management/lib/state';
import { TmDocument, TmRegisterDocument } from '@tenant-management/lib/types';
import { Observable } from 'rxjs';
import {
  selectCreditLimitAllDocuments,
  selectCreditLimitDetails,
} from '../../+state';
import { CreditLimitDetailsPageActions } from '../../+state/actions';
import { CreditLimitDetails } from '../../models/credit-limit-details';

@Component({
  selector: 'e2e-credit-limit-details',
  templateUrl: './credit-limit-details.component.html',
  styleUrls: ['./credit-limit-details.component.scss'],
})
export class CreditLimitDetailsComponent implements OnInit {
  getCreditLimitDetails$ = new Observable<CreditLimitDetails>();
  getDateFormat$ = new Observable<string>();
  getCreditLimitAllDocuments$ = new Observable<TmDocument[]>();

  constructor(private store: Store) {}

  ngOnInit(): void {
    this.getCreditLimitDetails$ = this.store.select(selectCreditLimitDetails);
    this.getDateFormat$ = this.store.select(selectDateFormat);
    this.getCreditLimitAllDocuments$ = this.store.select(
      selectCreditLimitAllDocuments
    );
  }

  getDocument(refId: string) {
    this.store.dispatch(TenantManagementLibPageActions.getDocument({ refId }));
  }

  uploadDocument(document: TmRegisterDocument) {
    this.store.dispatch(
      CreditLimitDetailsPageActions.sendLimitDetailsFilestackDocument({
        document,
      })
    );
  }
}
