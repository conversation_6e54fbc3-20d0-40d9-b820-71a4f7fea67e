import { Component, OnInit } from '@angular/core';
import {
  ApplicationStatuses,
  E2eEarlyRepaymentLoan,
  EarlyRepaymentTypes,
} from '@e2e/lib/types';
import { Store } from '@ngrx/store';
import {
  selectCurrencies,
  selectDateFormat,
} from '@tenant-management/lib/state';
import { dateRequestFormat } from '@tenant-management/lib/types';
import { ChartDatasetCustomTypesPerDataset } from 'chart.js';
import { format } from 'date-fns';
import { combineLatest, Observable } from 'rxjs';
import { filter, take } from 'rxjs/operators';

import {
  portfolios,
  selectApplicationStatus,
  selectEarlyRepaymentSimulate,
} from '../../+state';
import { ApplicationCreditLimitEarlyRepaymentPageActions } from '../../+state/actions';

@Component({
  selector: 'e2e-application-credit-limit-early-repayment-simulation',
  templateUrl:
    './application-credit-limit-early-repayment-simulation.component.html',
  styleUrls: [
    './application-credit-limit-early-repayment-simulation.component.scss',
  ],
})
export class ApplicationCreditLimitEarlyRepaymentSimulationComponent
  implements OnInit
{
  getCurrency$ = this.store.select(selectCurrencies);
  getDateFormat$ = this.store.select(selectDateFormat);
  getApplicationStatus$ = this.store.select(selectApplicationStatus);

  getEarlyRepaymentSimulate$ = new Observable<any>();
  portfolios$ = new Observable<any>();
  portfolios = {} as any;
  loans = [] as any;

  repaymentTypes = EarlyRepaymentTypes;
  applicationStatuses = ApplicationStatuses;

  constructor(private store: Store) {}

  ngOnInit(): void {
    this.getEarlyRepaymentSimulate$ = this.store.select(
      selectEarlyRepaymentSimulate
    );
    this.portfolios$ = this.store.select(portfolios);
    combineLatest([this.portfolios$, this.getApplicationStatus$])
      .pipe(
        filter(
          ([portfolios]) => !!portfolios && portfolios.portfolios?.length > 0
        ),
        take(1)
      )
      .subscribe(([portfolios, applicationStatus]) => {
        this.loans = this.getAllLoans(portfolios);
        if (
          this.loans.length > 0 &&
          applicationStatus === this.applicationStatuses.PendingApproval
        ) {
          this.store.dispatch(
            ApplicationCreditLimitEarlyRepaymentPageActions.getRequestSimulate({
              spreadMethod: portfolios.repaymentDistribution,
              effectiveDate: portfolios.effectiveDate,
              loans: this.loans,
              fees: portfolios.fees,
            })
          );
        }
      });
  }

  openFullRepaymentTable(
    loanId: string | number,
    effectiveDate: any,
    fees: any
  ) {
    const earlyRepayment: any = {
      loanId,
      effectiveDate: format(new Date(effectiveDate as Date), dateRequestFormat),
      fees: [],
    };

    fees.forEach((fee: any, index: any) => {
      const hasFixed = fee.fixedRate !== undefined;

      earlyRepayment.fees.push({
        type: fee.feeType,
        spreadMethod: fee.feeSpreadMethod,
        calcType: fee.calcType,
        minAmount: fee.minAmount,
        maxAmount: fee.maxAmount,
        currency: fee.currency,
        percentageRate: fee.percentageRate ? +fee.percentageRate : '',
        fixedAmount: hasFixed ? fee?.fixedRate : '',
        calculationBase: fee.calculationBase,
      } as any);
    });

    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanFullRepayments(
        earlyRepayment
      )
    );
  }

  openPartialRepaymentTable(
    loanId: string | number,
    effectiveDate: any,
    fees: any,
    amountToRepay: any,
    repaymentDistribution: any
  ) {
    const earlyRepayment: any = {
      loanId,
      effectiveDate: format(new Date(effectiveDate as Date), dateRequestFormat),
      fees: [],
      earlyRepaymentAmount: amountToRepay,
      spreadMethod: repaymentDistribution,
    };
    fees.forEach((fee: any, index: any) => {
      const hasFixed = fee.fixedRate !== undefined;
      earlyRepayment.fees.push({
        type: fee.feeType,
        spreadMethod: fee.feeSpreadMethod,
        calcType: fee.calcType,
        minAmount: fee.minAmount,
        maxAmount: fee.maxAmount,
        currency: fee.currency,
        percentageRate: fee.percentageRate ? +fee.percentageRate : '',
        // fixedAmount: fee.fixedAmount,
        fixedAmount: hasFixed ? fee?.fixedRate : '',
        calculationBase: fee.calculationBase,
      } as any);
    });

    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanPartialRepayments(
        earlyRepayment
      )
    );
  }

  openDebtRepaymentTable(
    amount: string | number,
    effectiveDate: any,
    limitId: any
  ) {
    this.store.dispatch(
      ApplicationCreditLimitEarlyRepaymentPageActions.getLoanDebtRepayments({
        limitId,
        effectiveDate: format(
          new Date(effectiveDate as Date),
          dateRequestFormat
        ),
        amount,
      })
    );
  }

  setSelectedPortfoliosChartDataset(
    selectedPortfolios: any
  ): ChartDatasetCustomTypesPerDataset<'doughnut'>[] {
    const { overdue, principalToBePaid, interestAmount, earlyRepaymentFee } =
      selectedPortfolios;

    return [
      {
        type: 'doughnut',
        data: [
          overdue.value as number,
          principalToBePaid as number,
          interestAmount as number,
          earlyRepaymentFee as number,
        ],
        backgroundColor: ['#e8381a', '#0050cf', '#40cdfa', '#71dd37'],
      },
    ];
  }

  getAllLoans(data: any): E2eEarlyRepaymentLoan[] {
    const allLoans: E2eEarlyRepaymentLoan[] = [];

    if (data?.portfolios) {
      for (const portfolio of data.portfolios) {
        if (portfolio.tracks) {
          for (const track of portfolio.tracks) {
            if (track.loans) {
              for (const loan of track.loans) {
                allLoans.push(loan);
              }
            }
          }
        }
      }
    }

    return allLoans;
  }

  getTooltip(loanId: number, earlyRepaymentSimulate: any) {
    if (!earlyRepaymentSimulate) return null;
    return (
      earlyRepaymentSimulate.find((loan: any) => loan.id === loanId) || null
    );
  }
}
