<div tabindex="0" class="p-5" *ngIf="allocationSimulation">
  <div class="flex justify-between items-start mb-4">
    <div class="text-[22px] font-medium text-compass">
      {{ 'e2e.allocationSimulation.title' | transloco }}
    </div>
    <div>
      <button
        mat-icon-button
        type="button"
        color="primary"
        class="me-2"
        [matTooltip]="'e2e.repaymentTable.saveAsXls' | transloco"
        (click)="exportToExcel(allocationSimulation)"
      >
        <mat-icon
          svgIcon="icon-download"
          class="icon-color icon-color-orientals"
        ></mat-icon>
      </button>

      <button mat-icon-button type="button" color="primary" mat-dialog-close>
        <mat-icon class="mat-tm-icon mat-tm-icon-size-24">close</mat-icon>
      </button>
    </div>
  </div>
  <mat-button-toggle-group
    *ngIf="showTabs"
    class="mat-button-toggle-group-horizontal-md mb-5"
    [formControl]="allocationType"
  >
    <mat-button-toggle value="allocationMonthlyCharges">
      {{ 'e2e.allocationSimulation.regularBtn' | transloco }}
    </mat-button-toggle>
    <mat-button-toggle value="allocationDebtCharges">
      {{ 'e2e.allocationSimulation.overdueBtn' | transloco }}
    </mat-button-toggle>
  </mat-button-toggle-group>

  <section class="sticky-header-table overflow-auto px-1 max-h-[550px]">
    <table
      class="w-full"
      aria-label="Allocation simulation table"
      mat-table
      [dataSource]="allocationSimulation"
      disableClear
    >
      <!-- [dataSource]="
          allocationSimulation?.[allocationTypeValue] ?? []
        " -->
      <!-- <ng-container matColumnDef="priority">
        <th mat-header-cell *matHeaderCellDef>
          {{ 'e2e.allocationSimulation.tableColumns.priority' | transloco }}
        </th>
        <td mat-cell *matCellDef="let payment">
          {{ payment.priority }}
        </td>
      </ng-container> -->

      <ng-container matColumnDef="level">
        <th mat-header-cell *matHeaderCellDef>
          {{ 'e2e.allocationSimulation.tableColumns.level' | transloco }}
        </th>
        <td mat-cell *matCellDef="let payment">
          {{ payment.level }}
        </td>
      </ng-container>

      <ng-container matColumnDef="portfolioReferenceName">
        <th mat-header-cell *matHeaderCellDef>
          {{
            'e2e.allocationSimulation.tableColumns.portfolioReferenceName'
              | transloco
          }}
        </th>
        <td mat-cell *matCellDef="let payment">
          <ng-container
            *ngIf="payment.portfolioReferenceName; else portfolioId"
          >
            {{ payment.portfolioReferenceName }}
          </ng-container>
          <ng-template #portfolioId>
            {{ payment.portfolioId ?? '--' }}</ng-template
          >
        </td>
      </ng-container>

      <ng-container matColumnDef="loanExternalName">
        <th mat-header-cell *matHeaderCellDef>
          {{
            'e2e.allocationSimulation.tableColumns.loanExternalName' | transloco
          }}
        </th>
        <td mat-cell *matCellDef="let payment">
          <ng-container *ngIf="payment.loanExternalName; else loanId">
            {{ payment.loanExternalName }}
          </ng-container>
          <ng-template #loanId> {{ payment.loanId ?? '--' }}</ng-template>
        </td>
      </ng-container>

      <ng-container matColumnDef="paymentComponentTypeName">
        <th mat-header-cell *matHeaderCellDef>
          {{
            'e2e.allocationSimulation.tableColumns.paymentComponentTypeName'
              | transloco
          }}
        </th>
        <td mat-cell *matCellDef="let payment">
          {{ payment.paymentComponentTypeName }}
        </td>
      </ng-container>

      <ng-container matColumnDef="originalComponentAmount">
        <th mat-header-cell *matHeaderCellDef>
          {{
            'e2e.allocationSimulation.tableColumns.originalComponentAmount'
              | transloco
          }}
        </th>
        <td mat-cell *matCellDef="let payment">
          {{ payment.originalComponentAmount | currency }}
        </td>
      </ng-container>

      <ng-container matColumnDef="actualComponentAmountPaid">
        <th mat-header-cell *matHeaderCellDef>
          {{
            'e2e.allocationSimulation.tableColumns.actualComponentAmountPaid'
              | transloco
          }}
        </th>
        <td mat-cell *matCellDef="let payment">
          {{ payment.actualComponentAmountPaid | currency }}
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="columns; sticky: true"></tr>
      <tr mat-row *matRowDef="let payment; columns: columns"></tr>
    </table>
  </section>
</div>
