import {
  ApplicationPaymentScheduleTypes,
  PaymentMethodTypes,
} from '@e2e/lib/types';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { selectDateFormat } from '@tenant-management/lib/state';
import { dateRequestFormat, TmStateSlices } from '@tenant-management/lib/types';
import { format } from 'date-fns';
import { PaymentStatuses } from '../../shared/enums/payment-statuses';
import { PaymentRequestDetails } from '../../shared/models/payment-request-details';
import { PaymentRequestProcesses } from '../../shared/models/payment-request-processes';
import { ReportDocument } from '../../shared/models/report-document';
import { ReportTypes } from '../enums/report-types';
import { Tabs } from '../enums/tabs';
import { AllocationSimulation } from '../models/allocation-simulation';
import { ManualPaymentProgress } from '../models/is-created-by-operator';
import { defaultRepaymentsCreditLimitsTableColumn } from '../utils/default-repayments-credit-limits-table-columns';
import { defaultRepaymentsDisbursementsTableColumn } from '../utils/default-repayments-disbursements-table-columns';
import { defaultRepaymentsLoansTableColumn } from '../utils/default-repayments-loans-table-columns';
import { defaultRepaymentsPortfoliosTableColumn } from '../utils/default-repayments-portfolios-table-columns';
import { PaymentRequestState } from './payment-request.reducer';

export const selectPaymentRequestState =
  createFeatureSelector<PaymentRequestState>(
    TmStateSlices.PaymentRequestsDetails
  );

export const selectPaymentRequestSlice = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest
);

export const selectPaymentRequestError = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest.error
);

export const selectRepayments = createSelector(
  selectPaymentRequestState,
  selectDateFormat,
  ({ paymentRequest, queryParams }, dateFormat) => {
    let activeFiltersCount = 0;

    Object.values(queryParams.filters).forEach((filterValue) => {
      if (filterValue) {
        ++activeFiltersCount;
      }
    });

    return {
      paymentRequest: paymentRequest.data,
      activeFiltersCount,
      queryParams,
      hasNoPaymentRequest:
        !paymentRequest.loading &&
        !paymentRequest.data &&
        !paymentRequest.error,
      dateFormat,
    };
  }
);

export const selectQueryParams = createSelector(
  selectPaymentRequestState,
  ({ queryParams }) => queryParams
);

export const selectPaymentRequestQueryParams = createSelector(
  selectPaymentRequestState,
  ({ queryParams }) => queryParams
);

export const selectPaymentRequestIsLoading = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest.loading
);

export const selectIsSidePanelShown = createSelector(
  selectPaymentRequestState,
  ({ isSidePanelShown }) => isSidePanelShown
);

export const selectPaymentStatus = createSelector(
  selectPaymentRequestState,
  ({ paymentStatus }) => paymentStatus
);

export const selectPaymentRequestId = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.id ?? ''
);

export const selectTotals = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest.data?.totals
);

export const selectCurrentLoanProduct = createSelector(
  selectPaymentRequestState,
  ({ selectedLoanProduct }) => {
    if (selectedLoanProduct && Object.keys(selectedLoanProduct).length) {
      return selectedLoanProduct;
    }
    return null;
  }
);

export const selectPaymentRequest = createSelector(
  selectPaymentRequestState,
  selectCurrentLoanProduct,
  selectDateFormat,

  ({ paymentRequest }, selectedProduct, dateFormat) => ({
    paymentRequest: paymentRequest.data,
    productVersion: selectedProduct?.productVersion,
    dateFormat,
  })
);

export const selectPaymentRequestTitleData = createSelector(
  selectPaymentRequestState,
  selectDateFormat,
  ({ paymentRequest }, dateFormat) => ({
    title: paymentRequest?.data?.name,
    date: paymentRequest?.data?.exactDate,
    dateFormat,
  })
);

export const selectPaymentRequestTotals = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.totals
);

export const selectPaymentPreview = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.paymentPreview
);

export const selectPaymentPreviewDetails = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest, paymentStatus }) =>
    ({
      createPortfolioAndLoanForDifference:
        paymentRequest.data?.createPortfolioAndLoanForDifference,
      paymentPreview: paymentRequest?.data?.paymentPreview,
      totals: paymentRequest?.data?.totals,
      status: paymentStatus,
      history: paymentRequest?.data?.history,
      financialSource: paymentRequest?.data?.financialSource,
    } as PaymentRequestDetails)
);

export const selectLoanProducts = createSelector(
  selectPaymentRequestState,
  ({ loanProducts }) =>
    loanProducts.filter(
      (product) =>
        product.paymentSchedule.defaultValue !==
        ApplicationPaymentScheduleTypes.Custom
    )
);

export const selectAnchors = createSelector(
  selectPaymentRequestState,
  ({ anchors }) => anchors
);

export const selectAnchorsEffectiveDates = createSelector(
  selectPaymentRequestState,
  ({ anchors }) =>
    anchors?.data?.map((anchor) =>
      format(new Date(anchor.effectiveDate), dateRequestFormat)
    )
);

export const selectAnchorsIsLoading = createSelector(
  selectPaymentRequestState,
  ({ anchors }) => anchors?.loading
);

export const selectSidePanelContextType = createSelector(
  selectPaymentRequestState,
  ({ sidePanelContextType }) => sidePanelContextType
);

export const selectManualRepaymentFail = createSelector(
  selectPaymentRequestState,
  ({ manualRepayment }) => !!manualRepayment?.error
);

export const selectRequestFromDate = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest.data?.fromDate
);

export const selectManualPaymentProgress = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest, paymentProgress }) =>
    ({
      createPortfolioAndLoanForDifference:
        paymentRequest?.data?.createPortfolioAndLoanForDifference,
      currentStep: paymentProgress?.data?.currentStep,
      status: paymentProgress?.data?.status,
    } as ManualPaymentProgress)
);

export const selectProgressId = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest, manualRepayment }) => {
    const repaymentProcess = paymentRequest.data?.linkedProcesses.find(
      (process) => process.operationType === PaymentMethodTypes.ManualRepayment
    );

    let progressId = repaymentProcess?.operationId;

    if (!progressId) {
      progressId = manualRepayment?.data?.id;
    }
    return {
      progressId: progressId as string,
    };
  }
);

export const selectOperationId = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    const repaymentProcess = paymentRequest.data?.linkedProcesses.find(
      (process) => process.operationType === PaymentMethodTypes.ManualRepayment
    );
    if (repaymentProcess) {
      return repaymentProcess.operationId;
    }
    return '';
  }
);

export const selectManualRepaymentProcesses = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    return paymentRequest.data?.linkedProcesses?.find(
      (process) => process.operationType === PaymentMethodTypes.ManualRepayment
    );
  }
);

export const selectLinkedProcessMasav = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    const generateMasavProcess = paymentRequest.data?.linkedProcesses?.filter(
      (process) => process.operationType === PaymentMethodTypes.generateMasav
    ) as PaymentRequestProcesses[];
    return paymentRequest.data?.linkedProcesses?.length
      ? generateMasavProcess[generateMasavProcess.length - 1]
      : undefined;
  }
);

export const selectLinkedProcesses = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest.data?.linkedProcesses || 0
);

export const selectIsLinkedProcessesEmpty = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    const linked = paymentRequest.data?.linkedProcesses;
    return Array.isArray(linked) && linked.length === 0;
  }
);

export const selectFailedPaymentRequestProcess = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    return paymentRequest.data?.linkedProcesses?.find(
      (process) => process.status === PaymentStatuses.Failed
    );
  }
);

export const selectDocuments = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    const reportDocuments = paymentRequest.data?.documents?.filter(
      (document) =>
        document.documentType !== ReportTypes.overview &&
        document.documentType !== ReportTypes.masav
    ) as ReportDocument[];
    return paymentRequest.data?.documents?.length
      ? reportDocuments[reportDocuments.length - 1]
      : undefined;
  }
);

export const selectStatusReportOverviewDocument = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => {
    const statusReportDocuments = paymentRequest.data?.documents?.filter(
      (document) => document.documentType === ReportTypes.overview
    ) as ReportDocument[];
    return paymentRequest.data?.documents?.length
      ? statusReportDocuments[statusReportDocuments.length - 1]
      : undefined;
  }
);

export const selectMasavOverviewDocument = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) =>
    paymentRequest.data?.documents?.filter(
      (document) => document.documentType === ReportTypes.masav
    ) as ReportDocument[]
);

export const selectCurrentTab = createSelector(
  selectPaymentRequestState,
  ({ currentTab }) => currentTab
);

export const selectSelectedLoansColumns = createSelector(
  selectPaymentRequestState,
  ({ loansColumns }) => {
    const columns = loansColumns || defaultRepaymentsLoansTableColumn;

    return columns
      ?.filter((column) => column.isSelected)
      .map((column) => column.name);
  }
);

export const selectLoansColumns = createSelector(
  selectPaymentRequestState,
  selectSelectedLoansColumns,
  ({ loansColumns }, selectedTableColumns) => {
    return {
      tableColumns: loansColumns,
      selectedTableColumns,
    };
  }
);

export const selectSelectedCreditLimitsColumns = createSelector(
  selectPaymentRequestState,
  ({ creditLimitsColumns }) => {
    const columns =
      creditLimitsColumns || defaultRepaymentsCreditLimitsTableColumn;

    return columns
      .filter((column) => column.isSelected)
      .map((column) => column.name);
  }
);

export const selectSelectedDisbursementsColumns = createSelector(
  selectPaymentRequestState,
  selectRepayments,
  ({ disbursementsColumns }, repayments) => {
    const columns =
      disbursementsColumns || defaultRepaymentsDisbursementsTableColumn;

    return columns
      .filter((column) => column.isSelected)
      .map((column) => column.name);
  }
);

export const selectDisbursementsColumns = createSelector(
  selectPaymentRequestState,
  selectSelectedDisbursementsColumns,
  ({ disbursementsColumns }, selectedTableColumns) => {
    return {
      tableColumns: disbursementsColumns,
      selectedTableColumns,
    };
  }
);

export const selectCreditLimitsColumns = createSelector(
  selectPaymentRequestState,
  selectSelectedCreditLimitsColumns,
  ({ creditLimitsColumns }, selectedTableColumns) => {
    return {
      tableColumns: creditLimitsColumns,
      selectedTableColumns,
    };
  }
);

export const selectSelectedPortfoliosColumns = createSelector(
  selectPaymentRequestState,
  ({ portfoliosColumns }) => {
    const columns = portfoliosColumns || defaultRepaymentsPortfoliosTableColumn;

    return columns
      .filter((column) => column.isSelected)
      .map((column) => column.name);
  }
);

export const selectPortfoliosColumns = createSelector(
  selectPaymentRequestState,
  selectSelectedPortfoliosColumns,
  ({ portfoliosColumns }, selectedTableColumns) => {
    return {
      tableColumns: portfoliosColumns,
      selectedTableColumns,
    };
  }
);

export const selectCurrentTabColumns = createSelector(
  selectLoansColumns,
  selectCreditLimitsColumns,
  selectPortfoliosColumns,
  selectDisbursementsColumns,
  selectCurrentTab,
  (
    loansColumns,
    creditLimitsColumns,
    portfoliosColumns,
    disbursementsColumns,
    currentTab
  ) => {
    let currentColumns = creditLimitsColumns;

    if (currentTab === Tabs.Loans) {
      currentColumns = loansColumns;
    }

    if (currentTab === Tabs.Portfolios) {
      currentColumns = portfoliosColumns;
    }

    if (currentTab === Tabs.Disbursement) {
      currentColumns = disbursementsColumns;
    }

    return currentColumns;
  }
);

export const selectDisbursementReferenceName = createSelector(
  selectPaymentRequestState,
  ({ disbursementReferenceName }) => disbursementReferenceName
);

export const selectExactDate = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.exactDate
);

export const selectCreatedAt = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.createdAt
);

export const selectProcessId = createSelector(
  selectPaymentRequestState,
  ({ processId }) => processId
);

export const selectHistory = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.history
);

export const selectPaymentRequestRepayments = createSelector(
  selectPaymentRequestState,
  ({ paymentRequest }) => paymentRequest?.data?.repayments
);
export const selectPaymentPreviewData = createSelector(
  selectPaymentRequestState,
  ({ paymentPreview }) => paymentPreview?.data
);

export const selectAllocationSimulation = createSelector(
  selectPaymentRequestState,
  ({ allocationSimulation }): AllocationSimulation | null =>
    allocationSimulation ?? null
);
