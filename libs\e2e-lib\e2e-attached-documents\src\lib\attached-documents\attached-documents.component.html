<div class="text-compass-60" [ngClass]="{ 'mb-2': addBottomMargin }">
  {{ title | transloco }}
</div>

<div class="flex flex-wrap">
  <ng-container *ngFor="let document of documents; trackBy: trackByFn">
    <div *ngIf="document.documentType === shownType || showAllTypes">
      <mat-icon
        [matTooltip]="document.fileName"
        svgIcon="icon-file"
        class="icon-color icon-color-raspberry me-2"
        role="button"
        (click)="getDocument(document.id)"
      ></mat-icon>
    </div>
  </ng-container>
</div>
