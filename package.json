{"name": "tenant-management-e2e-loans", "version": "1.11.0", "license": "MIT", "scripts": {"ng": "nx", "postinstall": "node ./decorate-angular-cli.js", "start": "nx run-many --target=serve --all --parallel=17", "build": "nx run-many --target=build --all", "build:dev": "nx run-many --target=build --all --configuration=dev", "test": "nx test", "pretty-quick": "pretty-quick --staged", "branch-name-validator": "chmod 755 .husky/branch-name-validator.sh && sh .husky/branch-name-validator.sh", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "private": true, "dependencies": {"@angular/animations": "15.2.9", "@angular/cdk": "15.2.9", "@angular/common": "15.2.9", "@angular/compiler": "15.2.9", "@angular/core": "15.2.9", "@angular/forms": "15.2.9", "@angular/material": "15.2.9", "@angular/material-date-fns-adapter": "15.2.9", "@angular/platform-browser": "15.2.9", "@angular/platform-browser-dynamic": "15.2.9", "@angular/router": "15.2.9", "@ngneat/transloco": "4.2.1", "@ngneat/transloco-persist-lang": "4.0.0", "@ngneat/transloco-persist-translations": "4.0.0", "@ngrx/effects": "15.4.0", "@ngrx/eslint-plugin": "15.4.0", "@ngrx/router-store": "15.4.0", "@ngrx/schematics": "15.4.0", "@ngrx/store": "15.4.0", "@ngrx/store-devtools": "15.4.0", "@nx/angular": "16.0.3", "@types/lodash": "4.14.182", "@uiowa/digit-only": "^3.2.1", "chart.js": "4.3.0", "date-fns": "2.29.3", "file-saver": "^2.0.5", "filestack-js": "^3.27.0", "flag-icons": "^7.2.0", "marked": "^4.3.0", "ngrx-store-localstorage": "^15.0.0", "ngx-cookie-service": "15.0.0", "ngx-doc-viewer": "15.0.1", "ngx-markdown": "^15.1.2", "ngx-mask": "15.1.5", "ngx-permissions": "15.0.1", "ngx-print": "1.3.1", "papaparse": "^5.4.1", "primeng": "15.2.0", "rxjs": "7.8.1", "tslib": "2.3.0", "xlsx": "^0.18.5", "zone.js": "0.12.0"}, "devDependencies": {"@admin-console/permissions": "~0.0.12", "@angular-devkit/build-angular": "15.2.8", "@angular-devkit/core": "15.2.8", "@angular-devkit/schematics": "15.2.8", "@angular-eslint/eslint-plugin": "15.0.0", "@angular-eslint/eslint-plugin-template": "15.0.0", "@angular-eslint/template-parser": "15.0.0", "@angular/cli": "15.2.0", "@angular/compiler-cli": "15.2.9", "@angular/language-service": "15.2.9", "@auth0/angular-jwt": "5.0.2", "@nx/cypress": "16.0.3", "@nx/eslint-plugin": "16.0.3", "@nx/jest": "16.0.3", "@nx/js": "16.0.3", "@nx/linter": "16.0.3", "@nx/web": "16.0.3", "@nx/workspace": "16.0.3", "@schematics/angular": "15.2.8", "@tenant-management/data-services": "^0.0.8", "@types/file-saver": "2.0.5", "@types/jest": "29.4.4", "@types/node": "16.11.7", "@types/papaparse": "^5.3.14", "@typescript-eslint/eslint-plugin": "5.58.0", "@typescript-eslint/parser": "5.58.0", "autoprefixer": "10.4.0", "cypress": "12.2.0", "eslint": "8.23.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "2.10.3", "husky": "7.0.4", "jest": "29.4.3", "jest-environment-jsdom": "29.4.3", "jest-preset-angular": "13.0.0", "lint-staged": "13.2.2", "ng-packagr": "15.2.2", "nx": "16.0.3", "nx-cloud": "16.1.0", "postcss": "8.4.5", "postcss-import": "14.1.0", "postcss-preset-env": "7.5.0", "postcss-url": "10.1.3", "prettier": "2.6.2", "prettier-plugin-organize-imports": "3.2.1", "pretty-quick": "3.1.3", "tailwindcss": "3.3.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "4.9.5"}, "prettier": {"singleQuote": true}}