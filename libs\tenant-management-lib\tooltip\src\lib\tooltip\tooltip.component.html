<ng-container *ngIf="getLayoutChanges$ | async as breakpointDesktop">
  <button
    type="button"
    class="mx-1 align-middle"
    [tooltipEvent]="breakpointDesktop.matches ? 'focus' : 'hover'"
    [escape]="escape"
    [tooltipPosition]="tooltipPosition"
    [positionStyle]="positionStyle"
    pTooltip="{{ escape ? tooltipMessage : tooltipTemplateMessage.innerHTML }}"
    [tooltipStyleClass]="isError ? 'p-tooltip-error' : ''"
    [hideDelay]="hideDelay"
  >
    <mat-icon
      [svgIcon]="icon"
      class="mat-tm-icon {{ iconSizeClass }} {{ iconColorClass }} {{
        icon === 'icon-info' ? '[&_path]:fill-orientals' : ''
      }}"
      [ngClass]="{ 'p-tooltip-error': isError }"
    >
      <ng-container *ngIf="matIcon">{{ matIcon }}</ng-container>
    </mat-icon>
  </button>
</ng-container>
