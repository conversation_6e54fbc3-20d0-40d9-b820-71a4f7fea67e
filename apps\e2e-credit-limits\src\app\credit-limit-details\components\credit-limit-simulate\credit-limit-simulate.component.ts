import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import {
  ApplicationFee,
  CreditLimitEarlyRepayment,
  E2eEarlyRepaymentLoan,
  E2eEarlyRepaymentOverview,
  E2eEarlyRepaymentPortfolio,
  EarlyRepaymentTypes,
  LoanDetail,
  LoanOverdueSummary,
  LoanPayment,
} from '@e2e/lib/types';
import { CreditLimitDetails } from '../../models/credit-limit-details';

import { Store } from '@ngrx/store';
import { selectCurrencies } from '@tenant-management/lib/state';
import { dateRequestFormat } from '@tenant-management/lib/types';
import { ChartDatasetCustomTypesPerDataset } from 'chart.js';
import { format } from 'date-fns';
import { combineLatest, filter, Observable, take, tap } from 'rxjs';
import {
  selectCreditLimitDetails,
  selectCreditLimitId,
  selectEarlyRepaymentForm,
  selectEarlyRepaymentSimulate,
  selectFees,
  selectOverview,
  selectSimulationRequested,
  selectSummedSelectedPortfolios,
} from '../../+state';
import { CreditLimitDetailsPageActions } from '../../+state/actions';
import { normalizePerecentageRate } from '../../utils';

interface SelectedPortfolio {
  portfolio: E2eEarlyRepaymentPortfolio;
  mode: EarlyRepaymentTypes | null;
}

@Component({
  selector: 'e2e-credit-limit-simulate',
  templateUrl: './credit-limit-simulate.component.html',
  styleUrls: ['./credit-limit-simulate.component.scss'],
})
export class CreditLimitSimulateComponent implements OnInit {
  EarlyRepaymentTypes = EarlyRepaymentTypes;

  // getSelectedPortfolios$ = new Observable<any>();
  getSelectedPortfolios$ = new Observable<SelectedPortfolio[]>();

  getLoanPayments$ = new Observable<{
    payments: MatTableDataSource<LoanPayment>;
    dateFormat: string;
  }>();
  getLoanDetails$ = new Observable<LoanDetail | undefined>();
  getOverview$ = new Observable<E2eEarlyRepaymentOverview | null>();
  getEarlyRepaymentForm$ = new Observable<CreditLimitEarlyRepayment>();
  getCreditLimitId$ = new Observable<string>();
  getCreditLimitDetails$ = new Observable<CreditLimitDetails>();
  getFees$ = new Observable<any>();
  getSimulationOverdue$ = new Observable<LoanOverdueSummary>();
  absFees = [] as any;
  getCurrency$ = new Observable<any>();
  getEarlyRepaymentSimulate$ = new Observable<any>();

  creditLimitId = '';
  loans = [] as any;

  constructor(
    private store: Store,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getCurrency$ = this.store.select(selectCurrencies);
    this.getSelectedPortfolios$ = this.store.select(
      selectSummedSelectedPortfolios
    );
    this.getOverview$ = this.store.select(selectOverview);
    this.getCreditLimitId$ = this.store.select(selectCreditLimitId);
    this.getEarlyRepaymentForm$ = this.store.select(selectEarlyRepaymentForm);
    combineLatest([
      this.getOverview$,
      this.getEarlyRepaymentForm$,
      this.getCreditLimitId$,
    ])
      .pipe(
        take(1),
        filter(
          ([overview, form, creditLimitId]) =>
            !overview && !form && !!creditLimitId
        )
      )
      .subscribe(([overview, form, creditLimitId]) => {
        this.router.navigateByUrl(`operations/credit-limits/${creditLimitId}`);
      });

    combineLatest([
      this.getOverview$,
      this.getEarlyRepaymentForm$,
      this.getCreditLimitId$,
      this.getSelectedPortfolios$,
    ])
      .pipe(take(1))
      .subscribe(([overview, form, creditLimitId, portfolios]) => {
        this.loans = this.getAllLoans(portfolios);

        const feesData = [] as ApplicationFee[];

        form?.fees.forEach((fee, index) => {
          const feePercentage = normalizePerecentageRate(fee.percentageRate);
          feesData.push({
            id: fee.id,
            feeName: fee.name,
            feeType: fee.type,
            calcType: fee.calcType,
            minFee: fee.minAmount,
            maxFee: fee.maxAmount,
            currency: fee.currency,
            // Save fees percentage rate divided by 100 because we display it multiply by 100
            percentageRate: feePercentage || '',
            fixedRate: fee.fixedAmount,
            calculationBase: fee.calculationBase,
            feeLevel: 'LOAN',
            isEnabled: fee.isActive,
            feeSpreadMethod: fee.spreadMethod,
            paymentDate: '',
            chargeAmount: this.getChargeAmount(fee),
          } as ApplicationFee);
        });

        if (this.loans.length > 0) {
          this.store.dispatch(
            CreditLimitDetailsPageActions.getRequestSimulate({
              limitId: creditLimitId,
              spreadMethod: form?.repaymentDistribution,
              effectiveDate: format(
                new Date(form.effectiveDate as Date),
                dateRequestFormat
              ),
              loans: this.loans,
              fees: feesData,
            })
          );
        } else {
          this.store.dispatch(
            CreditLimitDetailsPageActions.clearCreditLimitEarlyRepaymentSimulate()
          );
        }
      });

    this.getEarlyRepaymentSimulate$ = this.store.select(
      selectEarlyRepaymentSimulate
    );

    this.getCreditLimitDetails$ = this.store.select(selectCreditLimitDetails);
    // this.getFees$ = this.store.select(selectFees);
    this.getFees$ = this.store.select(selectFees).pipe(
      tap((fees) => {
        if (fees) {
          this.absFees = fees;
        }
      })
    );

    const sub = this.store
      .select(selectSimulationRequested)
      .pipe(filter((flag) => flag))
      .subscribe(() => {
        this.dispatchSimulationRequest();
        sub.unsubscribe();
      });
  }

  dispatchSimulationRequest(): void {
    combineLatest([
      this.getEarlyRepaymentForm$,
      this.getSelectedPortfolios$,
      this.getOverview$,
    ])
      .pipe(take(1))
      .subscribe(([form, portfolios, overview]) => {
        // TODO Portfolios are mandatory if early repayment type of form if FULL or earlyRepaymentForm.amountToRepay > overview.totalDebtAmount

        const filteredPortfolios = this.filterPortfoliosWithLoans(portfolios);

        // const usedPortfolios =
        //   form.repaymentType === EarlyRepaymentTypes.Full ||
        //   (overview &&
        //     form.amountToRepay &&
        //     form.amountToRepay > overview?.totalDebtAmount)
        //     ? filteredPortfolios
        //     : // : overview?.portfolios;
        //       overview?.portfolios.map(({ tracks, ...rest }) => rest);

        const useFilteredPortfolios =
          form.repaymentType === EarlyRepaymentTypes.Full ||
          (overview &&
            form.amountToRepay &&
            form.amountToRepay > overview?.totalDebtAmount);

        const usedPortfolios = useFilteredPortfolios
          ? filteredPortfolios // SelectedPortfolio[]
          : overview?.portfolios.map(({ tracks, ...rest }) => rest); // E2eEarlyRepaymentPortfolio[]

        const feesData = [] as ApplicationFee[];

        form?.fees.forEach((fee, index) => {
          const feePercentage = normalizePerecentageRate(fee.percentageRate);
          feesData.push({
            id: fee.id,
            feeName: fee.name,
            feeType: fee.type,
            calcType: fee.calcType,
            minFee: fee.minAmount,
            maxFee: fee.maxAmount,
            currency: fee.currency,
            // Save fees percentage rate divided by 100 because we display it multiply by 100
            percentageRate: feePercentage || '',
            fixedRate: fee.fixedAmount,
            calculationBase: fee.calculationBase,
            feeLevel: 'LOAN',
            isEnabled: fee.isActive,
            feeSpreadMethod: fee.spreadMethod,
            paymentDate: '',
            chargeAmount: this.getChargeAmount(fee),
          } as ApplicationFee);
        });
        const usedFees =
          form.repaymentType === EarlyRepaymentTypes.Full ||
          (overview &&
            form.amountToRepay &&
            form.amountToRepay > overview?.totalDebtAmount)
            ? feesData
            : null;

        const earlyRepaymentSimulationRequest = {
          // tags: '',
          applicant: {
            id: form.relatedParty,
            type: 'IDENTITY_PARTY',
          },
          amount: {
            // If full of the form -> totalAmountFullRepayment, IF partial of the form -> amount from the form
            value:
              form?.repaymentType === EarlyRepaymentTypes.Full
                ? overview?.totalAmountFullRepayment
                : form?.amountToRepay,
            currency: overview?.currency,
          },
          moneyAllocation: 'MANUAL',
          earlyRepaymentType: form.repaymentType,
          spreadMethod: form.repaymentDistribution,
          requestedBy: {
            identityPartyId: form.relatedParty,
            // requestDate: form.requestDate,
            requestDate: format(
              new Date(form.requestDate as Date),
              dateRequestFormat
            ),
          },
          // effectiveDate: form.effectiveDate,
          effectiveDate: format(
            new Date(form.effectiveDate as Date),
            dateRequestFormat
          ),
          payers: [form.payer],
          // creditLimit: CreditLimit,
          creditLimit: {
            // From overview
            id: overview?.id,
            externalId: overview?.externalId,
            creditType: overview?.creditType,
            referenceName: overview?.referenceName,
            limitAmount: overview?.limitAmount,
            utilizedAmount: overview?.utilizedAmount,
            unutilizedAmount: overview?.unutilizedAmount,
            totalAmountFullRepayment: overview?.totalAmountFullRepayment,
            currency: overview?.currency,
            effectiveDate: overview?.effectiveDate,
            endDate: overview?.endDate,
            status: overview?.status,
          },
          // Filter if track has not loans there are not visible
          // portfolios: usedPortfolios,
          // ...(usedPortfolios && { portfolios: usedPortfolios }),
          // ...(usedPortfolios && {
          //   portfolios: usedPortfolios.map((entry) => entry.portfolio),
          // }),
          portfolios: useFilteredPortfolios
            ? filteredPortfolios.map((entry) => entry.portfolio) // extract E2eEarlyRepaymentPortfolio[]
            : usedPortfolios ?? [],
          ...(usedFees && { fees: usedFees }),
        };

        this.store.dispatch(
          CreditLimitDetailsPageActions.sendSimulationRequest({
            earlyRepaymentSimulationRequest,
          })
        );

        this.store.dispatch(
          CreditLimitDetailsPageActions.resetCreditLimitEarlyRepaymentState()
        );

        // TODO reset flag if needed
        this.store.dispatch(
          CreditLimitDetailsPageActions.clearSimulationRequest()
        );
      });
  }

  setSelectedPortfoliosChartDataset(
    selectedPortfolios: any
  ): ChartDatasetCustomTypesPerDataset<'doughnut'>[] {
    const { overdue, principalToBePaid, interestAmount, earlyRepaymentFee } =
      selectedPortfolios;
    return [
      {
        type: 'doughnut',
        data: [
          overdue.value as number,
          principalToBePaid as number,
          interestAmount as number,
          earlyRepaymentFee as number,
        ],
        backgroundColor: ['#e8381a', '#0050cf', '#40cdfa', '#71dd37'],
      },
    ];
  }

  openFullRepaymentTable(loanId: string | number) {
    combineLatest([this.getEarlyRepaymentForm$])
      .pipe(take(1))
      .subscribe(([form]) => {
        const transformedFees = form.fees.map((fee) => ({
          ...fee,
          percentageRate:
            fee.calcType === 'PERCENTAGE' &&
            typeof fee.percentageRate === 'number'
              ? fee.percentageRate / 100
              : fee.percentageRate,
        }));
        this.store.dispatch(
          CreditLimitDetailsPageActions.getLoanFullRepayments({
            loanId,
            effectiveDate: format(
              new Date(form.effectiveDate as Date),
              dateRequestFormat
            ),
            fees: transformedFees,
          })
        );
      });
  }

  openPartialRepaymentTable(
    loanId: string | number,
    earlyRepaymentAmount: number
  ) {
    combineLatest([this.getEarlyRepaymentForm$])
      .pipe(take(1))
      .subscribe(([form]) => {
        const transformedFees = form.fees.map((fee) => ({
          ...fee,
          percentageRate:
            fee.calcType === 'PERCENTAGE' &&
            typeof fee.percentageRate === 'number'
              ? fee.percentageRate / 100
              : fee.percentageRate,
        }));
        this.store.dispatch(
          CreditLimitDetailsPageActions.getLoanPartialRepayments({
            loanId,
            effectiveDate: format(
              new Date(form.effectiveDate as Date),
              dateRequestFormat
            ),
            fees: transformedFees,
            earlyRepaymentAmount,
            spreadMethod: form.repaymentDistribution,
          })
        );
      });
  }

  openDebtRepaymentTable(amount: string | number) {
    combineLatest([this.getEarlyRepaymentForm$, this.getCreditLimitId$])
      .pipe(take(1))
      .subscribe(([form, limitId]) => {
        this.store.dispatch(
          CreditLimitDetailsPageActions.getLoanDebtRepayments({
            limitId,
            effectiveDate: format(
              new Date(form.effectiveDate as Date),
              dateRequestFormat
            ),
            amount,
          })
        );
      });
  }

  filterPortfoliosWithLoans(data: SelectedPortfolio[]): SelectedPortfolio[] {
    return data
      .map((entry) => {
        const portfolio = entry.portfolio;

        // Филтрирай tracks с поне един loan
        const tracksWithLoans = (portfolio.tracks || []).filter(
          (track) => track.loans && track.loans.length > 0
        );

        // Ако няма такива tracks, не включвай този portfolio
        if (tracksWithLoans.length === 0) return null;

        return {
          ...entry,
          portfolio: {
            ...portfolio,
            tracks: tracksWithLoans,
          },
        };
      })
      .filter((entry): entry is SelectedPortfolio => entry !== null);
  }

  private getChargeAmount(currentFee: any): number {
    // const fees =
    //   this.loanSimulatedRepayment?.payments[0]?.originalPaymentSchedule
    //     .componentCharge?.fees || [];

    for (const fee of this.absFees) {
      if (fee.type === currentFee.type) {
        return +fee.amount;
      }
    }
    return 0;
  }

  getAllLoans(data: any): E2eEarlyRepaymentLoan[] {
    const allLoans: E2eEarlyRepaymentLoan[] = [];
    if (Array.isArray(data)) {
      for (const item of data) {
        const portfolio = item.portfolio;
        if (portfolio?.tracks) {
          for (const track of portfolio.tracks) {
            if (track.loans) {
              for (const loan of track.loans) {
                allLoans.push(loan);
              }
            }
          }
        }
      }
    }

    return allLoans;
  }

  getTooltip(loanId: number, earlyRepaymentSimulate: any) {
    if (!earlyRepaymentSimulate) return null;
    return (
      earlyRepaymentSimulate.find((loan: any) => loan.id === loanId) || null
    );
  }
}
