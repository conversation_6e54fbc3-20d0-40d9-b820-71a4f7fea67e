import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Inject,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { LanguageService } from '@tenant-management/lib/services';
import {
  selectAuthDetails,
  TmLibPartakerChatPageActions,
} from '@tenant-management/lib/state';
import { take } from 'rxjs';
import { PartakerChatLayoutComponent } from '../partaker-chat-layout/partaker-chat-layout.component';

@Component({
  selector: 'tm-partaker-chat-button',
  templateUrl: './partaker-chat-button.component.html',
  styleUrls: ['./partaker-chat-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PartakerChatButtonComponent implements OnInit, AfterViewInit {
  @ViewChild('btn', { read: ViewContainerRef, static: true })
  vcr!: ViewContainerRef;
  basePrefix = '';

  private iconPaths = {
    frontDefault: 'assets/pictures/partaker-chat/ai-btn-front-default.png',
    frontHover: 'assets/pictures/partaker-chat/ai-btn-front-hover.png',
    btnDefault: 'assets/pictures/partaker-chat/ai-btn-default.png',
    btnHover: 'assets/pictures/partaker-chat/ai-btn-hover.png',
    sparksDefault: 'assets/pictures/partaker-chat/ai-btn-sparks-static.png',
    sparksHover: 'assets/pictures/partaker-chat/ai-btn-sparks-animation.gif',
  };

  private sessionId!: string | null;
  private authDetails$ = this.store.select(selectAuthDetails).pipe(take(1));
  private vboId!: string;
  constructor(
    private overlay: Overlay,
    private languageService: LanguageService,
    private elRef: ElementRef,
    private store: Store,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit() {
    this.authDetails$.pipe(take(1)).subscribe((data) => {
      this.vboId = data.loginParams?.tempVboId || '';
    });
  }

  ngAfterViewInit() {
    this.basePrefix =
      this.document.querySelector('base')?.getAttribute('href') || '/';
    this.elRef.nativeElement.style.setProperty(
      '--front-default',
      `url(${this.basePrefix}${this.iconPaths.frontDefault})`
    );
    this.elRef.nativeElement.style.setProperty(
      '--front-hover',
      `url(${this.basePrefix}${this.iconPaths.frontHover})`
    );
    this.elRef.nativeElement.style.setProperty(
      '--btn-default',
      `url(${this.basePrefix}${this.iconPaths.btnDefault})`
    );
    this.elRef.nativeElement.style.setProperty(
      '--btn-hover',
      `url(${this.basePrefix}${this.iconPaths.btnHover})`
    );
    this.elRef.nativeElement.style.setProperty(
      '--sparks-default',
      `url(${this.basePrefix}${this.iconPaths.sparksDefault})`
    );
    this.elRef.nativeElement.style.setProperty(
      '--sparks-hover',
      `url(${this.basePrefix}${this.iconPaths.sparksHover})`
    );
  }

  open(): void {
    if (!this.sessionId) {
      this.sessionId = crypto.randomUUID();
    }
    const positionStrategy = this.overlay
      .position()
      .global()
      .end('0px')
      .top('0px');

    const overlayRef: OverlayRef = this.overlay.create({
      hasBackdrop: false,
      width: '100%',
      height: '100vh',
      positionStrategy,
      scrollStrategy: this.overlay.scrollStrategies.block(),
      direction: this.languageService.getDirection(),
    });

    const portal = new ComponentPortal(PartakerChatLayoutComponent, this.vcr);
    const componentRef = overlayRef.attach(portal);
    this.store.dispatch(
      TmLibPartakerChatPageActions.setSessionId({ sessionId: this.sessionId })
    );
    componentRef.instance.sessionId = this.sessionId;
    componentRef.instance.vboId = this.vboId;
    componentRef.instance.closeChat.subscribe((clearSession: boolean) => {
      // if(clearSession) {
      //   this.sessionId = null;
      // }
      overlayRef.dispose();
    });
  }
}
