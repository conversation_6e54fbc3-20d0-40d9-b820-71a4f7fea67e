import { Component, Inject, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import {
  AllocationCharge,
  AllocationSimulation,
  LoanRepaymentDetails,
} from '@e2e/lib/types';
import { TranslocoService } from '@ngneat/transloco';
import { Store } from '@ngrx/store';
import * as saveAs from 'file-saver';
import * as XLSX from 'xlsx';

@Component({
  selector: 'e2e-debt-simulation',
  templateUrl: './debt-simulation.component.html',
  styleUrls: ['./debt-simulation.component.scss'],
})
export class DebtSimulationComponent implements OnInit {
  constructor(
    private store: Store,
    private translocoService: TranslocoService,

    // 3) mat dialog
    @Inject(MAT_DIALOG_DATA)
    private _loanRepayment: LoanRepaymentDetails
  ) {}

  allocationSimulation = [] as any;
  allocationType = new FormControl('allocationMonthlyCharges');
  showTabs = false;

  get allocationTypeValue(): keyof AllocationSimulation {
    return this.allocationType.value as keyof AllocationSimulation;
  }

  columns: string[] = [
    // Hide priority column
    // 'priority',
    'level',
    'portfolioReferenceName',
    'loanExternalName',
    'paymentComponentTypeName',
    'originalComponentAmount',
    'actualComponentAmountPaid',
  ];

  ngOnInit(): void {
    this.allocationSimulation = this._loanRepayment;
  }

  exportToExcel(
    allocationSimulation: any,
    fileName: string = 'allocation_simulation'
  ) {
    const headers = this.getHeaderTranslations();

    let allocationMonthlyCharges: (string | number | Date)[][] = [];
    // let allocationDebtCharges: (string | number | Date)[][] = [];

    if (allocationSimulation) {
      allocationMonthlyCharges = this.buildFileRows(
        allocationSimulation,
        headers
      );
    }

    // if (allocationSimulation['allocationDebtCharges']) {
    //   allocationDebtCharges = this.buildFileRows(
    //     allocationSimulation['allocationDebtCharges'],
    //     headers
    //   );
    // }

    // Convert CSV data to a worksheet
    const allocationMonthlyChargesWorksheet: XLSX.WorkSheet =
      XLSX.utils.aoa_to_sheet(allocationMonthlyCharges);
    // const allocationDebtChargesWorksheet: XLSX.WorkSheet =
    //   XLSX.utils.aoa_to_sheet(allocationDebtCharges);

    // Create a new workbook and append the worksheet
    const workbook: XLSX.WorkBook = XLSX.utils.book_new();

    if (allocationMonthlyCharges.length > 0) {
      XLSX.utils.book_append_sheet(
        workbook,
        allocationMonthlyChargesWorksheet,
        'Regular'
      );
    }

    // if (allocationDebtCharges.length > 0) {
    //   XLSX.utils.book_append_sheet(
    //     workbook,
    //     allocationDebtChargesWorksheet,
    //     'Overdue'
    //   );
    // }

    // Write the workbook and create a Blob
    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Save the file
    saveAs(blob, `${fileName}.xlsx`);
  }

  private getHeaderTranslations() {
    const headers = [...this.columns];
    headers.forEach(
      (header, index) =>
        (headers[index] = this.translocoService.translate(
          `e2e.allocationSimulation.tableColumns.${header}`
        ))
    );

    return headers;
  }

  private buildFileRows(data: AllocationCharge[], headers: string[]) {
    return data.reduce(
      (accumulator: (string | number | Date)[][], payment) => {
        return [
          ...accumulator,
          [
            // Hide priority column
            // payment.priority,
            payment.level,
            payment.portfolioReferenceName ?? payment.portfolioId,
            payment.loanExternalName ?? payment.loanId,
            payment.paymentComponentTypeName,
            payment.originalComponentAmount,
            payment.actualComponentAmountPaid,
          ],
        ];
      },
      [headers]
    );
  }
}
