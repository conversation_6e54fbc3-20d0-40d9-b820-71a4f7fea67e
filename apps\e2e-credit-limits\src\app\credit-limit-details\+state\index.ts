import {
  filterCreditLimitEarlyRepaymentPortfolios,
  sumCreditLimitEarlyRepaymentPortfolios,
} from '@e2e/lib/state';
import {
  BorrowerInfoDetail,
  BorrowerPortfolio,
  CreditLimitStatuses,
  defaultPortfoliosSort,
  E2eActionsMenu,
  E2eCustomActionsMenu,
  E2eCustomActionsMenuTypes,
  E2eDetailsTab,
  sortBorrowersByRole,
  sortFees,
  transformBorrowerDetails,
} from '@e2e/lib/types';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { selectCreditLimitActions } from '@tenant-management/lib/state';
import { TmStateSlices } from '@tenant-management/lib/types';
import { SelectedPortfolio } from '../components/early-repayment-money-allocation/early-repayment-money-allocation.component';
import { CreditLimitDetails } from '../models/credit-limit-details';
import { CreditLimitLoan } from '../models/credit-limit-loan';
import { CreditLimitsResponse } from '../models/credit-limits-response';
import { CreditType } from '../models/credit-types';
import { CreditLimitDetailsState } from './credit-limit-details.reducer';

export const selectCreditLimitDetailsState =
  createFeatureSelector<CreditLimitDetailsState>(
    TmStateSlices.CreditLimitDetails
  );

export const selectCreditLimitsDetails = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimits }) => {
    let details = {} as CreditLimitsResponse;

    if (creditLimits?.data) {
      details = creditLimits.data[0];
    }

    return details;
  }
);

export const selectCreditLimitId = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.limitId
);

export const selectCustomerId = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.customerId
);

export const selectCreditLimitDetails = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitDetails }) => {
    let updatedCreditLimitDetails = {} as CreditLimitDetails;
    if (Object.keys(creditLimitDetails).length) {
      updatedCreditLimitDetails = { ...creditLimitDetails };
      const { portfoliosCreatedAmount, amount } = updatedCreditLimitDetails;
      let calculatedPercent =
        (+portfoliosCreatedAmount.value / +amount.value) * 100;
      // In case the percentage is between 99.5 and 99.9 - set 99%
      if (calculatedPercent < 100 && calculatedPercent > 99.5) {
        calculatedPercent = 99;
      } else {
        calculatedPercent = Math.round(calculatedPercent);
      }

      updatedCreditLimitDetails.calculatedPercent = calculatedPercent;
    }
    return updatedCreditLimitDetails;
  }
);

export const selectBorrowerPortfolioDetails = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitPortfolioDetails }) => creditLimitPortfolioDetails
);

export const selectLoanIdentityParties = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitOwner }) => {
    return creditLimitOwner;
  }
);

export const selectCreditLimitOwnerFullDetails = createSelector(
  selectLoanIdentityParties,
  (loanIdentityParties) => {
    const sortedIdentities = sortBorrowersByRole(
      loanIdentityParties.loanIdentityParties
    );

    return sortedIdentities?.map((loanIdentityParty) => {
      return {
        ...loanIdentityParty,
        identityParty: transformBorrowerDetails(
          loanIdentityParty.identityParty,
          loanIdentityParty.role
        ),
      };
    });
  }
);

export const selectCreditLimitOwner = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitOwner }) => creditLimitOwner
);

export const selectConsumerIdentityIds = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.consumerIdentityIds
);

export const selectLoanDetails = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.creditLimitConsumers.data
);

export const selectCreditLimitConsumersDetails = createSelector(
  selectLoanDetails,
  (creditLimitConsumers) =>
    creditLimitConsumers?.map((creditLimitConsumer) => {
      return transformBorrowerDetails(creditLimitConsumer);
    })
);

export const selectConsumerIdentityId = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.consumerIdentityIds[0]
);

export const selectConsumer = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitConsumers }) => creditLimitConsumers.data
);

export const selectBorrowerCurrentPortfolioId = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitCurrentPortfolioId }) => creditLimitCurrentPortfolioId
);

export const selectBorrowerPortfoliosAndDetails = createSelector(
  selectCreditLimitDetailsState,
  selectBorrowerCurrentPortfolioId,
  (
    { creditLimitPortfolios: { data, loading }, creditLimitPortfolioDetails },
    currentPortfolioId
  ) => ({
    portfolios: data?.portfolios || [],
    currentPortfolioId,
    portfolioDetails: creditLimitPortfolioDetails,
    isLoading: loading,
  })
);

export const selectBorrowerCurrentPortfolioDetails = createSelector(
  selectBorrowerPortfoliosAndDetails,
  ({ portfolioDetails, currentPortfolioId }) => {
    let currentPortfolio: BorrowerPortfolio | null = null;

    for (const key in portfolioDetails) {
      const portfolio = portfolioDetails[key];

      if (portfolio.id === currentPortfolioId) {
        currentPortfolio = portfolio;
      }
    }

    return currentPortfolio;
  }
);

export const selectHasPortfolios = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitPortfolios }) => !!Object.keys(creditLimitPortfolios).length
);

export const selectCurrentPortfolio = createSelector(
  selectBorrowerCurrentPortfolioDetails,
  (currentPortfolioDetails) => {
    return {
      redirectUrl: `operations/credit-limits`,
      currentPortfolioDetails,
    };
  }
);

export const selectCreditLimitPortfolios = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitPortfolios: { data } }) => data
);
export const selectCreditLimitPortfoliosIsLoading = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitPortfolios: { loading } }) => loading
);

export const selectCurrentPortfolioRequestDetails = createSelector(
  selectBorrowerCurrentPortfolioId,
  selectCreditLimitPortfolios,
  (portfolioId, portfoliosData) => {
    let updatedPortfolioIndex = -1;

    if (portfoliosData) {
      updatedPortfolioIndex = portfoliosData.portfolios.findIndex(
        (portfolio) => portfolio.id === portfolioId
      );
    }

    return {
      portfolioId,
      portfolioIndex: updatedPortfolioIndex,
    };
  }
);

export const selectCreditLimitActionsMenu = createSelector(
  selectCreditLimitActions,
  selectCreditLimitDetailsState,
  (
    { actions },
    { creditLimitDetails: { status, portfoliosCreatedAmount } }
  ) => {
    const isDisabled =
      status === CreditLimitStatuses.Canceled ||
      status === CreditLimitStatuses.Closed;
    const isCreditLimitEarlyRepayment = true;
    const actionMenus: E2eActionsMenu[] = [
      {
        btnName: 'general.buttons.changeLimitAmount',
        url: 'change-amount',
        isDisabled,
        isVisible: actions.showChangeCreditLimitAmount,
      },
      {
        btnName: 'general.buttons.earlyRepayment',
        url: 'early-repayment',
        isDisabled: isDisabled || portfoliosCreatedAmount.value === 0,
        isVisible: actions.showEarlyRepaymentCreditLimit,
        isCreditLimitEarlyRepayment,
      },
      {
        btnName: 'general.buttons.letterOfIntent',
        url: 'letter-of-intent',
        isDisabled: false,
        isVisible: actions.showLetterOfIntent,
      },
    ];
    const customActionMenus: E2eCustomActionsMenu[] = [
      {
        btnName: 'general.buttons.cancelLimitAmount',
        isDisabled,
        actionType: E2eCustomActionsMenuTypes.CancelLimitApplication,
        isVisible: actions.showCancelCreditLimit,
      },
    ];

    return { actionMenus, customActionMenus };
  }
);

export const selectSnackBarMessage = createSelector(
  selectCreditLimitDetailsState,
  ({ snackBarMessage }) => snackBarMessage
);

export const selectPortfoliosSort = createSelector(
  selectCreditLimitDetailsState,
  ({ portfoliosSort }) => portfoliosSort || defaultPortfoliosSort
);

export const selectBorrowerPrepayments = createSelector(
  selectCreditLimitDetailsState,
  ({ prepayments }) => prepayments
);

export const selectProductDetails = createSelector(
  selectCreditLimitDetailsState,
  (state) => {
    return {
      ...state.productDetails,
      portfolioProducts: {
        ...state.productDetails?.portfolioProducts,
        tracks: state.productDetails?.portfolioProducts?.tracks?.map(
          (track) => {
            return {
              ...track,
              fees: sortFees(track.fees),
            };
          }
        ),
      },
    };
  }
);

export const selectProductTracks = createSelector(
  selectProductDetails,
  (productDetails) => productDetails?.portfolioProducts?.tracks
);

export const selectCollaterals = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitCollaterals }) => creditLimitCollaterals?.data
);

export const selectCollateralId = createSelector(
  selectCreditLimitDetailsState,
  ({ collateralId }) => collateralId
);

export const selectCollateralOwnerId = createSelector(
  selectCreditLimitDetailsState,
  ({ collateralOwnerId }) => collateralOwnerId
);

export const selectCollateralOwner = createSelector(
  selectCollaterals,
  selectCollateralId,
  selectCollateralOwnerId,
  (creditLimitCollaterals, collateralId, collateralOwnerId) => {
    // return {};
    const currentCollateral = creditLimitCollaterals?.find(
      ({ externalCollateralId }) => externalCollateralId === collateralId
    );
    const owner = currentCollateral?.owners.find(
      ({ id }) => id === collateralOwnerId
    );

    return owner;
  }
);

export const selectCollateralOwnerIdentity = createSelector(
  selectCollateralOwner,
  (owner) => {
    if (owner?.identityParty) {
      return transformBorrowerDetails(owner?.identityParty);
    }
    return {} as BorrowerInfoDetail;
  }
);

export const selectSelectedTrackId = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.selectedTrackId
);
export const selectCreditLimitPreviewTabs = createSelector(
  selectCreditLimitDetailsState,
  selectCollaterals,
  (state, collaterals) => {
    // TODO Update the selector
    const links: E2eDetailsTab[] = [
      {
        icon: 'icon-cube',
        name: 'credit-limit-details.matTabLinks.details',
        url: './details',
        isVisible: true,
      },
      {
        icon: 'icon-wallet-2',
        name: 'credit-limit-details.matTabLinks.portfolios',
        url: './portfolios',
        isVisible: true,
      },
      {
        icon: 'icon-person',
        name: 'credit-limit-details.matTabLinks.borrower',
        url: './borrower',
        isVisible: true,
      },
      {
        icon: 'icon-consumers',
        name: 'credit-limit-details.matTabLinks.consumers',
        url: './consumers',
        isVisible: !(
          state.creditLimitDetails.creditType === CreditType.MORTGAGE
        ),
      },
      {
        icon: 'icon-security',
        name: 'credit-limit-details.matTabLinks.collaterals',
        url: './collaterals',
        isVisible: !!collaterals?.length,
      },
      {
        icon: 'icon-cash-in-hand',
        name: 'credit-limit-details.matTabLinks.loans',
        url: './loans',
        isVisible: true,
      },
    ];

    return links;
  }
);

export const selectIdentityPartiesLifeInsurances = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.lifeInsuranceCoverage.data?.identityPartyLifeInsurances || []
);

export const selectedCurrentProductTrackReferenceName = createSelector(
  selectCreditLimitDetailsState,
  ({ currentProductTrackReferenceName }) => currentProductTrackReferenceName
);

export const selectFees = createSelector(
  selectCreditLimitDetailsState,
  (state) => state.fees?.data
);
export const selectOverview = createSelector(
  selectCreditLimitDetailsState,
  ({ overview }) => overview
);
export const selectSimulationOverdue = createSelector(
  selectOverview,

  (overview) => ({
    currency: overview?.currency,
    overdueSummary: overview?.portfolios.map((portfolio) => ({
      portfolioReferenceName: portfolio.referenceName,
      totalDebtAmount: portfolio.debtAmount,
      portfolioId: portfolio.id,
    })),
  })
);

// export const selectSelectedPortfolios = createSelector(
//   selectCreditLimitDetailsState,
//   // ({selectedPortfolios}) => Object.values(selectedPortfolios)
//   // ({selectedPortfolios}) => selectedPortfolios
//   ({selectedPortfolios}): SelectedPortfolio[] => Object.values(selectedPortfolios)
// );

export const selectSelectedPortfolios = createSelector(
  selectCreditLimitDetailsState,
  (state): SelectedPortfolio[] => Object.values(state.selectedPortfolios || {})
);

export const selectSummedSelectedPortfolios = createSelector(
  selectSelectedPortfolios,
  (portfolios): SelectedPortfolio[] => {
    const filteredPortfolios =
      filterCreditLimitEarlyRepaymentPortfolios(portfolios);
    const summedPortfolios = filteredPortfolios.map(
      (item: SelectedPortfolio) => {
        return {
          ...item,
          portfolio: sumCreditLimitEarlyRepaymentPortfolios([
            item.portfolio,
          ])[0],
        };
      }
    );
    return summedPortfolios;
  }
);

export const isPortfolioSelected = (portfolioId: string) =>
  createSelector(
    selectCreditLimitDetailsState,
    ({ selectedPortfolios }) => !!selectedPortfolios[portfolioId]
  );

export const selectEarlyRepaymentForm = createSelector(
  selectCreditLimitDetailsState,
  ({ earlyRepaymentForm }) => earlyRepaymentForm
);

export const selectTotalSelectedLoanAmount = createSelector(
  selectCreditLimitDetailsState,
  ({ totalSelectedLoanAmount }) =>
    totalSelectedLoanAmount !== undefined && totalSelectedLoanAmount !== null
      ? totalSelectedLoanAmount
      : null
);

export const selectSimulationRequested = createSelector(
  selectCreditLimitDetailsState,
  ({ simulationRequested }) => simulationRequested
);

export const selectPayers = createSelector(
  selectCreditLimitDetailsState,
  ({ overview }) => {
    if (overview?.portfolios) {
      return overview?.portfolios[0]?.tracks[0]?.payers || [];
    }
    return [];
  }
);

export const selectAmountToRepay = createSelector(
  selectCreditLimitDetailsState,
  ({ amountToRepay }) => amountToRepay
);

export const selectCreditLimitAmount = createSelector(
  selectCreditLimitDetails,
  ({ amount }) => amount
);

export const selectCreditLimitDocuments = createSelector(
  selectCreditLimitDetails,
  (creditLimitDetails) => creditLimitDetails?.documents
);

export const selectIsFormValid = createSelector(
  selectCreditLimitDetailsState,
  ({ isFormValid }) => isFormValid
);

export const selectEarlyRepaymentSimulate = createSelector(
  selectCreditLimitDetailsState,
  ({ earlyRepaymentSimulate }) => earlyRepaymentSimulate
);

export const selectCreditLimitLoans = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitLoans }) => creditLimitLoans.data
);

export const selectCreditLimitLoansIsLoading = createSelector(
  selectCreditLimitDetailsState,
  ({ creditLimitLoans }) => creditLimitLoans?.loading
);

export const selectTransformedCreditLimitLoans = createSelector(
  selectCreditLimitLoans,
  (creditLimitLoansData) => {
    if (!creditLimitLoansData?.portfolios) {
      return [];
    }

    const creditLimitLoans: CreditLimitLoan[] = [];

    creditLimitLoansData.portfolios.forEach((portfolio) => {
      portfolio.tracks.forEach((track) => {
        (track.loans || []).forEach((loan) => {
          creditLimitLoans.push({
            loanId: loan.id,
            loan: loan.referenceName,
            product: track.product,
            originalPrincipal: {
              value: loan.loanAmount,
              currency: creditLimitLoansData.currency,
            },
            outstandingPrincipal: {
              value: loan.outstandingPrincipal,
              currency: creditLimitLoansData.currency,
            },
            earlyRepayment: {
              value: loan.amountFullRepayment,
              currency: creditLimitLoansData.currency,
            },
            interestRate: loan.regularInterestRate,
            disbursementDate: loan.disbursementDate,
            maturityDate: loan.maturityDate,
            status: loan.status,
            statusReason: loan.statusReason,
          });
        });
      });
    });

    return creditLimitLoans;
  }
);
export const selectLetterOfIntent = createSelector(
  selectCreditLimitDetailsState,
  ({ letterOfIntent }) => letterOfIntent
);

export const selectLetterOfIntentDocuments = createSelector(
  selectLetterOfIntent,
  (letterOfIntent) => letterOfIntent?.documents
);

export const selectCreditLimitAllDocuments = createSelector(
  selectCreditLimitDocuments,
  selectLetterOfIntentDocuments,
  (creditLimitDocuments, letterOfIntentDocuments) => [
    ...(creditLimitDocuments || []),
    ...(letterOfIntentDocuments || []),
  ]
);
