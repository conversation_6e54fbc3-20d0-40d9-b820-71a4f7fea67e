import {
  ApplicationCreditLimitEarlyRepayment,
  ApplicationCreditLimitEarlyRepaymentSimulate,
  BorrowerDetail,
} from '@e2e/lib/types';
import { createReducer, on } from '@ngrx/store';
import { getLimitEarlyRepaymentApplicationSuccess } from '../+state/actions/application-credit-limit-early-repayment-api.actions';

import {
  SnackBarData,
  SnackBarMessageTypes,
} from '@tenant-management/lib/types';
import { ApplicationCreditLimitEarlyRepaymentPageActions } from '../+state/actions';
import * as ApplicationCreditLimitEarlyRepaymentApiActions from '../+state/actions/application-credit-limit-early-repayment-api.actions';

export interface ApplicationCreditLimitEarlyRepaymentState {
  application: ApplicationCreditLimitEarlyRepayment;
  applicationId: string;
  identityParties: BorrowerDetail[];
  snackBarMessage: SnackBarData;
  earlyRepaymentSimulate: ApplicationCreditLimitEarlyRepaymentSimulate;
}

export const initialState: ApplicationCreditLimitEarlyRepaymentState = {
  application: {} as ApplicationCreditLimitEarlyRepayment,
  applicationId: '',
  identityParties: [],
  snackBarMessage: {} as SnackBarData,
  earlyRepaymentSimulate: {} as ApplicationCreditLimitEarlyRepaymentSimulate,
};

export const ApplicationCreditLmitEarlyRepaymentReducer = createReducer(
  initialState,
  on(
    ApplicationCreditLimitEarlyRepaymentPageActions.setApplicationId,
    (state, { applicationId }): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        applicationId,
      };
    }
  ),
  on(
    getLimitEarlyRepaymentApplicationSuccess,
    (
      state,
      { application, identityParties }
    ): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        application,
        identityParties: identityParties,
      };
    }
  ),
  on(
    ApplicationCreditLimitEarlyRepaymentApiActions.sendApplicationDecisionSuccess,
    (state): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        snackBarMessage: {
          message: 'general.snackBarMessages.default.success',
          messageType: SnackBarMessageTypes.Success,
        },
      };
    }
  ),
  on(
    ApplicationCreditLimitEarlyRepaymentApiActions.getCreditLimitearlyRepaymentDetailsSuccess,
    (
      state,
      { creditLimitOverdue }
    ): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        application: {
          ...state.application,
          creditLimit: {
            ...state.application.creditLimit,
            overdue: creditLimitOverdue,
          },
        },
      };
    }
  ),
  on(
    ApplicationCreditLimitEarlyRepaymentPageActions.getRequestSimulate,
    (state): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        earlyRepaymentSimulate:
          {} as ApplicationCreditLimitEarlyRepaymentSimulate,
      };
    }
  ),
  on(
    ApplicationCreditLimitEarlyRepaymentApiActions.getRequestSimulateSuccess,
    (
      state,
      { earlyRepaymentSimulate }
    ): ApplicationCreditLimitEarlyRepaymentState => {
      return {
        ...state,
        earlyRepaymentSimulate,
      };
    }
  )
);
