import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { ENVIRONMENT, Environment } from '@tenant-management/lib/environments';
import {
  HttpState,
  PartakerChatMessage,
  withHttpState,
} from '@tenant-management/lib/types';
import { filter, map, Observable, of, switchMap, take, timer } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PartakerChatService {
  // private messageSubject = new Subject<string>();
  // private sseUrl!: string;
  constructor(
    private http: HttpClient,
    @Inject(ENVIRONMENT) private environment: Environment
  ) {}

  sendMessage(sessionId: string, text: string): Observable<any> {
    const url = `${this.environment.chatPrefix}/v1/chat/${sessionId}/message`;
    return this.http
      .post<{ text: string }>(url, { text }, { observe: 'response' })
      .pipe(
        switchMap(
          (
            response: HttpResponse<{
              text: string;
              inProgress?: boolean;
              reply?: string;
            }>
          ) => {
            const body = response.body;
            // if the backend already returned finished, just emit the reply
            if (body && !body.inProgress) {
              return of(body.reply);
            }
            // Long poll /message every second until inProgress is false
            return timer(0, 3000).pipe(
              switchMap(() =>
                this.http.get<{ inProgress: boolean; reply: string }>(url)
              ),
              // only when inProgress flips to false do we continue
              filter(
                (res: { inProgress: boolean; reply: string }) => !res.inProgress
              ),
              take(1),
              map((res: { reply: string }) => res.reply)
            );
          }
        )
      );
  }

  // receiveMessage() {
  //   return this.messageSubject.asObservable();
  // }

  sendMessageOld(message: string): Observable<HttpState<PartakerChatMessage>> {
    return timer(3000).pipe(
      map(
        () =>
          ({
            text: 'Quibusdam similique commodi adipisci odio repellendus, pariatur asperiores obcaecati porro, quo libero voluptas eveniet.',
            timestamp: new Date().getTime(),
            type: 'ai',
          } as PartakerChatMessage)
      ),
      withHttpState()
    );
  }
}
