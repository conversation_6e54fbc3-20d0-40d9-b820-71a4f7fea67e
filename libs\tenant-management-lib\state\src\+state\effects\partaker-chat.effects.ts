import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { PartakerChatService } from '@tenant-management/lib/services';
import { catchError, exhaustMap, filter, map, of, tap } from 'rxjs';
import {
  TmLibPartakerChatApiActions,
  TmLibPartakerChatPageActions,
} from '../actions';

@Injectable()
export class PartakerChatEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private partakerChatService: PartakerChatService
  ) {}

  sendChatMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TmLibPartakerChatPageActions.sendMessage),
      filter(({ text }) => !!text),
      tap(() =>
        this.store.dispatch(TmLibPartakerChatPageActions.messageSent())
      ),
      exhaustMap(({ text, sessionId }) =>
        this.partakerChatService.sendMessage(sessionId, text.text).pipe(
          map((response) => {
            return TmLibPartakerChatApiActions.sendMessageSuccess({
              response: { data: response, loading: false },
            });
          }),
          catchError((error: unknown) => {
            return of(
              TmLibPartakerChatApiActions.sendMessageFailure({
                error,
              })
            );
          })
        )
      )
    )
  );
}
