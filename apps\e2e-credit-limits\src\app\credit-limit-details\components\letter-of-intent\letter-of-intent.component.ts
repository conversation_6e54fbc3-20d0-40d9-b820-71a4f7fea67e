import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  FormControl,
  NonNullableFormBuilder,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { addMonths, endOfDay, isWithinInterval, startOfDay } from 'date-fns';
import { CreditLimitDetailsPageActions } from '../../+state/actions';

@Component({
  selector: 'e2e-letter-of-intent',
  templateUrl: './letter-of-intent.component.html',
  styleUrls: ['./letter-of-intent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LetterOfIntentComponent {
  letterOfIntentForm = this.fb.group({
    organizationName: '',
    valueDate: ['', Validators.required],
  });

  get valueDateField() {
    return this.letterOfIntentForm.get('valueDate') as FormControl<
      Date | string
    >;
  }
  constructor(
    private fb: NonNullableFormBuilder,
    private store: Store,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  filterAvailableDates(targetDate: Date | null): boolean {
    if (!targetDate) {
      return false;
    }

    const today = new Date();
    const rangeStart = startOfDay(today);
    const rangeEnd = endOfDay(addMonths(today, 1));

    return isWithinInterval(targetDate, {
      start: rangeStart,
      end: rangeEnd,
    });
  }

  generateLetterOfIntent() {
    this.letterOfIntentForm.markAllAsTouched();

    if (this.letterOfIntentForm.valid) {
      const letterOfIntent = this.letterOfIntentForm.getRawValue();
      this.store.dispatch(
        CreditLimitDetailsPageActions.generateLetterOfIntent({
          letterOfIntent,
        })
      );

      this.router.navigate(['..'], { relativeTo: this.route });
    }
  }
}
