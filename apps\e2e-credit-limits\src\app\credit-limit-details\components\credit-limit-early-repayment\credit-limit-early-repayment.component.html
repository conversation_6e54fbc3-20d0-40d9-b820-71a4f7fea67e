<ng-container *ngIf="getCreditLimitId$ | async">
  <ng-container *ngIf="!isEarlyRepaymentSimulate">
    <mat-accordion>
      <mat-expansion-panel
        class="mb-4 shadow-primary rounded-lg"
        expanded
        #panel
      >
        <mat-expansion-panel-header class="items-center">
          <mat-panel-title class="block text-2xl text-compass-87 font-semibold">
            <div>
              {{ 'credit-limit-details.earlyRepayment.title' | transloco }}
            </div>
          </mat-panel-title>
          <ng-container *ngIf="getOverview$ | async as overview">
            <div class="me-6">
              <div class="flex">
                <div class="flex flex-col">
                  <span
                    class="text-compass-87 uppercase text-xs font-medium me-2 mt-2"
                  >
                    {{
                      'credit-limit-details.earlyRepayment.creditLimitBalance'
                        | transloco
                    }}
                  </span>
                  <span
                    class="text-compass-87 uppercase text-xs font-medium me-2 mt-3"
                  >
                    {{
                      'credit-limit-details.earlyRepayment.totalOverdueAmount'
                        | transloco
                    }}
                  </span>
                </div>
                <div class="flex flex-col">
                  <span class="text-compass text-lg font-semibold">
                    {{
                      overview.totalAmountFullRepayment
                        | currency: overview.currency
                    }}
                  </span>
                  <span class="text-compass text-lg font-semibold text-once">
                    {{ overview.totalDebtAmount | currency: overview.currency }}
                  </span>
                </div>
              </div>
            </div>
          </ng-container>
        </mat-expansion-panel-header>

        <ng-container *ngIf="(getEarlyRepaymentForm$ | async) !== undefined">
        </ng-container>

        <ng-container
          *ngIf="getExistingApplications$ | async as existingApplications"
        >
          <div class="mb-6">
            <e2e-existing-application
              *ngIf="existingApplications.length"
              [existingApplication]="existingApplications[0]"
              [isSideView]="true"
              message="e2e.existingApplication.changeCreditLimitApplication"
            ></e2e-existing-application>
          </div>

          <form
            *ngIf="(onFormChange$ | async) !== undefined"
            [formGroup]="earlyRepaymentForm"
            [ngClass]="{ 'pointer-events-none': existingApplications.length }"
          >
            <ng-container
              *ngIf="
                getAllowedOnlyDebtRepayment$ | async as allowedOnlyDebtRepayment
              "
            >
            </ng-container>

            <div class="grid grid-cols-4 gap-4">
              <div class="text-base text-compass font-semibold mt-2 -mb-2">
                <span>
                  {{
                    'credit-limit-details.earlyRepayment.typeOfEarlyRepayment'
                      | transloco
                  }}
                </span>
              </div>

              <div class="col-span-4 grid grid-cols-4 gap-4">
                <mat-radio-group
                  formControlName="repaymentType"
                  class="mat-radio-group-horizontal mb-4"
                  (change)="onChangeRepaymentType()"
                >
                  <mat-radio-button
                    [value]="repaymentTypes.Full"
                    class="flex-1"
                  >
                    <span class="text-base text-compass-87">
                      {{
                        'credit-limit-details.earlyRepayment.full' | transloco
                      }}
                    </span>
                  </mat-radio-button>
                  <mat-radio-button
                    [value]="repaymentTypes.Partial"
                    class="flex-1"
                  >
                    <span class="text-base text-compass-87">
                      {{
                        'credit-limit-details.earlyRepayment.partial'
                          | transloco
                      }}
                    </span>
                  </mat-radio-button>
                </mat-radio-group>

                <!-- Effective Date -->
                <div class="h-0">
                  <mat-form-field appearance="outline" class="w-full">
                    <mat-label>{{
                      'credit-limit-details.earlyRepayment.effectiveDate'
                        | transloco
                    }}</mat-label>
                    <input
                      matInput
                      [matDatepicker]="picker2"
                      (dateChange)="changedEffectiveDate($event)"
                      formControlName="effectiveDate"
                      [max]="todayDate"
                    />
                    <mat-datepicker-toggle
                      matIconSuffix
                      [for]="picker2"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker2></mat-datepicker>
                  </mat-form-field>
                </div>

                <!-- Amount to Repay -->
                <div class="h-0" *ngIf="getCurrency$ | async as currency">
                  <mat-form-field
                    *ngIf="repaymentTypeField.value === repaymentTypes.Partial"
                    appearance="outline"
                    class="w-full"
                  >
                    <mat-label>{{
                      'credit-limit-details.earlyRepayment.amountToRepay'
                        | transloco
                    }}</mat-label>
                    <input
                      class="text-base"
                      matInput
                      formControlName="amountToRepay"
                      (input)="getAmount()"
                      mask="separator.2"
                      thousandSeparator=","
                      [prefix]="
                        currency[0].code
                          ? onGetCurrencySymbol(currency[0].code)
                          : ''
                      "
                    />
                    <mat-error
                      *ngIf="
                        repaymentAmountToRepayField.invalid &&
                        repaymentAmountToRepayField.touched
                      "
                      class="mb-4"
                    >
                      <ng-container
                        *ngIf="repaymentAmountToRepayField.hasError('max')"
                      >
                        {{
                          'credit-limit-details.earlyRepayment.amountErrorMessage'
                            | transloco
                        }}
                      </ng-container>
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>

              <!-- Second Row: 4 Columns -->
              <div class="col-span-4 grid grid-cols-4 gap-4">
                <!-- Related Party -->
                <div
                  *ngIf="
                    selectCreditLimitIdentityParties$
                      | async as creditLimitOwner
                  "
                >
                  <mat-form-field appearance="outline" class="w-full">
                    <mat-label>{{
                      'credit-limit-details.earlyRepayment.relatedParty'
                        | transloco
                    }}</mat-label>
                    <mat-select
                      formControlName="relatedParty"
                      name="relatedParty"
                      panelClass="custom-panel"
                    >
                      <mat-option
                        *ngFor="
                          let identityParty of creditLimitOwner.loanIdentityParties
                        "
                        [value]="identityParty.identityParty?.id"
                      >
                        <div class="flex items-center">
                          <ng-container
                            *ngIf="
                              identityParty.identityParty.type ===
                                borrowerTypes.Personal;
                              else companyName
                            "
                          >
                            <div>
                              {{ identityParty.identityParty?.person?.name }}
                              {{
                                identityParty.identityParty?.person?.familyName
                              }}
                            </div>
                          </ng-container>

                          <ng-template #companyName>
                            <div>
                              {{ identityParty.identityParty?.company?.name }}
                            </div>
                          </ng-template>

                          <!-- <mat-icon class="mx-2 mat-tm-icon mat-tm-icon-size-8"
                        >lens</mat-icon
                      > -->
                          -
                          <!-- <div>{{ identityParty.role }}</div> -->
                          <div>
                            {{
                              'credit-limit-details.borrowerRoles.' +
                                identityParty.role
                                | transloco
                                | uppercase
                            }}
                          </div>
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <!-- Request Date -->
                <div
                  *ngIf="
                    selectCreditLimitIdentityParties$
                      | async as creditLimitOwner
                  "
                >
                  <mat-form-field appearance="outline" class="w-full">
                    <mat-label>{{
                      'credit-limit-details.earlyRepayment.requestDate'
                        | transloco
                    }}</mat-label>
                    <input
                      matInput
                      [matDatepicker]="picker1"
                      formControlName="requestDate"
                      [max]="todayDate"
                    />
                    <mat-datepicker-toggle
                      matIconSuffix
                      [for]="picker1"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                  </mat-form-field>
                </div>

                <!-- Payers -->
                <div *ngIf="selectPayers$ | async as selectPayers">
                  <mat-form-field appearance="outline" class="w-full">
                    <mat-label>{{
                      'credit-limit-details.earlyRepayment.payer' | transloco
                    }}</mat-label>

                    <mat-select
                      formControlName="payer"
                      name="payer"
                      panelClass="custom-panel"
                    >
                      <mat-option
                        *ngFor="let payer of selectPayers"
                        [value]="payer"
                      >
                        <div class="flex items-center">
                          <div>
                            {{ payer.identityPartyName }}
                          </div>
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>

              <!-- Third Row: 3 Columns -->
              <div
                class="col-span-4 grid grid-cols-3 gap-4"
                [class.hidden]="(repaymentAndFeesVisible$ | async) === false"
              >
                <!-- Repayment Distribution -->
                <div *ngIf="(getAllowedOnlyDebtRepayment$ | async) === false">
                  <div class="text-base text-compass font-semibold mb-3">
                    {{
                      'credit-limit-details.earlyRepayment.repaymentDistribution'
                        | transloco
                    }}
                  </div>
                  <mat-radio-group
                    formControlName="repaymentDistribution"
                    class="flex flex-col gap-2"
                  >
                    <mat-radio-button
                      [value]="spreadMethods.ReducePaymentNumber"
                      [ngClass]="{
                'border-chili': repaymentDistributionField.invalid && repaymentDistributionField.touched,                
              }"
                    >
                      <span class="text-base text-compass-87">
                        {{
                          'credit-limit-details.earlyRepayment.keepRepaymentAmount'
                            | transloco
                        }}
                      </span>

                      <tm-tooltip
                        [escape]="false"
                        [tooltipTemplateMessage]="repaymentAmountTemplate"
                      >
                        <div #repaymentAmountTemplate>
                          <div class="w-[300px]">
                            {{
                              'e2e.earlyRepayment.keepRepaymentAmountInfo'
                                | transloco
                            }}
                          </div>
                        </div>
                      </tm-tooltip>
                    </mat-radio-button>
                    <mat-radio-button
                      [ngClass]="{
                'border-chili': repaymentDistributionField.invalid && repaymentDistributionField.touched,                
              }"
                      [value]="spreadMethods.ReduceMonthlyPayment"
                    >
                      <span class="text-base text-compass-87">
                        {{
                          'credit-limit-details.earlyRepayment.keepMaturityDate'
                            | transloco
                        }}
                      </span>

                      <tm-tooltip
                        [escape]="false"
                        [tooltipTemplateMessage]="maturityDateTemplate"
                      >
                        <div #maturityDateTemplate>
                          <div class="w-[300px]">
                            {{
                              'e2e.earlyRepayment.KeepMaturityDateInfo'
                                | transloco
                            }}
                          </div>
                        </div>
                      </tm-tooltip>
                    </mat-radio-button>
                  </mat-radio-group>
                </div>
                <ng-container *ngIf="getCreditLimitFees$ | async as fees">
                  <div formArrayName="fees">
                    <div
                      *ngFor="let fee of fees; let i = index"
                      [formGroupName]="i"
                      class="grid grid-cols-2 items-center"
                    >
                      <div class="me-2">
                        <mat-slide-toggle
                          formControlName="isActive"
                          (change)="changePercentageFee(fee.feeType, $event)"
                          ><span class="ps-2 inline-block">{{
                            fee.name
                          }}</span></mat-slide-toggle
                        >
                      </div>

                      <div *ngIf="fee?.percentage?.percentageRate" class="mt-5">
                        <mat-form-field class="block">
                          <mat-label>
                            {{ 'e2e.earlyRepayment.charge' | transloco }}
                          </mat-label>

                          <input
                            (change)="changePercentageFee(fee.feeType)"
                            matInput
                            formControlName="percentageRate"
                            suffix="%"
                            mask="separator"
                            thousandSeparator=","
                          />
                        </mat-form-field>
                      </div>

                      <div *ngIf="fee.fixed?.fixedRate" class="mt-5">
                        <mat-form-field class="block">
                          <mat-label>
                            {{ 'e2e.earlyRepayment.charge' | transloco }}
                          </mat-label>

                          <input
                            matInput
                            formControlName="fixedAmount"
                            [prefix]="
                              fee.currency
                                ? onGetCurrencySymbol(fee.currency)
                                : ''
                            "
                            mask="separator"
                            thousandSeparator=","
                          />
                        </mat-form-field>
                      </div>

                      <div
                        *ngIf="fee.calcType && fee.calcType === 'AUTOMATIC'"
                        class="mt-5"
                      >
                        <mat-form-field class="block">
                          <mat-label>
                            {{ 'e2e.earlyRepayment.charge' | transloco }}
                          </mat-label>

                          <input matInput formControlName="calcType" />
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </form>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>

    <ng-container *ngIf="getOverview$ | async as overview">
      <ng-container *ngIf="!earlyRepaymentForm.invalid">
        <e2e-early-repayment-money-allocation
          [totalFeeAmount]="totalFixedFeesAmount"
          [repaymentTypeFieldValue]="repaymentTypeField.value"
          [earlyRepaymentOverview]="(getOverview$ | async) || null"
          [amountToRepay]="amountToRepay"
        ></e2e-early-repayment-money-allocation>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-container>
