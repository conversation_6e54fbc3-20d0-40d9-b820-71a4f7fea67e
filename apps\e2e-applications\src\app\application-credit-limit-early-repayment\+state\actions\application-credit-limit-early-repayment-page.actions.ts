import { ApplicationDecisionRequest, ApplicationTypes } from '@e2e/lib/types';
import { createAction, props } from '@ngrx/store';

export const getCreditLimitEarlyRepaymentApplication = createAction(
  '[Application credit limit early repayment step PAGE] Get credit limit early repayment application',
  props<{ applicationType: ApplicationTypes }>()
);
export const getIdentityParties = createAction(
  '[Application credit limit early repayment parties PAGE] Get identity parties',
  props<{ application: any }>()
);
export const sendApplicationDecision = createAction(
  '[Application decision credit limit early repayment PAGE] Send application decision credit limit early repayment',
  props<{ applicationDecision: ApplicationDecisionRequest }>()
);

export const setApplicationId = createAction(
  '[Application credit limit early repayment parties PAGE] Set application id',
  props<{ applicationId: string }>()
);

export const getCreditLimitEarlyRepaymnetOverdue = createAction(
  '[Application credit limit early repayment overdue PAGE]Get total overdue',
  props<{ creditLimitId: number }>()
);

export const getLoanFullRepayments = createAction(
  '[Application credit limit early repayment PAGE] Get loan full repayments simulation',
  props<{ loanId: string | number; effectiveDate: any; fees: any }>()
);

export const getLoanPartialRepayments = createAction(
  '[Application credit limit early repayment PAGE] Get loan partial repayments simulation',
  props<{
    loanId: string | number;
    effectiveDate: any;
    fees: any;
    earlyRepaymentAmount: any;
    spreadMethod: any;
  }>()
);

export const getLoanDebtRepayments = createAction(
  '[Application credit limit early repayment PAGE] Get debt repayment simulation',
  props<{
    limitId: any;
    effectiveDate: Date | string;
    amount: any;
  }>()
);

export const getRequestSimulate = createAction(
  '[Application credit limit early repayment PAGE] Get request simulation',
  props<{
    spreadMethod: any;
    effectiveDate: any;
    loans: any;
    fees: any;
  }>()
);
