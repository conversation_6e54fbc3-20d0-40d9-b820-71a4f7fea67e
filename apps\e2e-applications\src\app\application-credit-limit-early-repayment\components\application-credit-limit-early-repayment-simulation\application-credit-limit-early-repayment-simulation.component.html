<ng-container *ngIf="portfolios$ | async as simulationDetails">
  <ng-container *ngIf="getDateFormat$ | async as dateFormat">
    <ng-container *ngIf="getCurrency$ | async as currency">
      <ng-container
        *ngIf="getApplicationStatus$ | async as getApplicationStatus"
      >
        <ng-container
          *ngIf="getEarlyRepaymentSimulate$ | async as earlyRepaymentSimulate"
        >
          <div
            class="mb-6 main-card"
            *ngIf="
              earlyRepaymentSimulate?.effectiveDate &&
              getApplicationStatus === applicationStatuses.PendingApproval
            "
          >
            <div class="grid grid-cols-5 gap-4">
              <div class="grid col-span-2 mx-4">
                <div class="text-compass-87 font-semibold text-2xl">
                  {{
                    'application-details.credit-limit-early-repayment.requestSimulation'
                      | transloco
                  }}
                </div>

                <div class="flex items-center">
                  <mat-icon
                    svgIcon="icon-calendar"
                    class="icon-color icon-color-compass mat-tm-icon mat-tm-icon-size-21 me-4"
                  ></mat-icon>

                  <div>
                    <div class="text-compass-60 text-sm">
                      {{
                        'application-details.credit-limit-early-repayment.effectiveDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-compass font-semibold text-sm">
                      {{
                        earlyRepaymentSimulate.effectiveDate
                          | date: 'dd.MM.yyyy'
                      }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="grid col-span-3 mx-4">
                <div class="flex items-center">
                  <div class="h-36 w-36 relative me-4">
                    <canvas
                      [e2eDoughnutChart]="
                        setSelectedPortfoliosChartDataset(
                          earlyRepaymentSimulate
                        )
                      "
                    ></canvas>

                    <div class="absolute w-full text-center top-1/4">
                      <div
                        class="text-xs mx-auto text-compass-60 w-1/2 font-medium"
                      >
                        {{
                          'application-details.credit-limit-early-repayment.totalRepayment'
                            | transloco
                        }}
                      </div>
                      <div class="text-lg text-compass-87 font-bold">
                        {{
                          earlyRepaymentSimulate.totalRepaymentAmount
                            | currency: currency[0].code
                        }}
                      </div>
                    </div>
                  </div>

                  <div class="w-full">
                    <div class="flex items-center mb-2 justify-between">
                      <div class="flex items-center">
                        <mat-icon
                          class="mx-1.5 text-thermic mat-tm-icon mat-tm-icon-size-10"
                        >
                          lens
                        </mat-icon>
                        <div class="text-sm text-compass-60">
                          {{
                            'application-details.credit-limit-early-repayment.overdueAmount'
                              | transloco
                          }}
                        </div>
                      </div>

                      <div
                        class="text-base text-compass-87 font-bold text-thermic"
                      >
                        {{
                          earlyRepaymentSimulate?.overdue?.value
                            | currency: currency[0].code
                        }}
                      </div>
                    </div>

                    <div class="flex items-center mb-2 justify-between">
                      <div class="flex items-center">
                        <mat-icon
                          class="mx-1.5 text-pacificBridge mat-tm-icon mat-tm-icon-size-10"
                        >
                          lens
                        </mat-icon>
                        <div class="text-sm text-compass-60">
                          {{
                            'application-details.credit-limit-early-repayment.principal'
                              | transloco
                          }}
                        </div>
                      </div>
                      <div class="text-base text-compass-87 font-bold">
                        {{
                          earlyRepaymentSimulate.principalToBePaid
                            | currency: currency[0].code
                        }}
                      </div>
                    </div>

                    <div class="flex items-center mb-2 justify-between">
                      <div class="flex items-center">
                        <mat-icon
                          class="mx-1.5 text-thunder mat-tm-icon mat-tm-icon-size-10"
                        >
                          lens
                        </mat-icon>

                        <div class="text-sm text-compass-60">
                          {{
                            'application-details.credit-limit-early-repayment.interest'
                              | transloco
                          }}
                        </div>
                      </div>
                      <div class="text-base text-compass-87 font-bold">
                        {{
                          earlyRepaymentSimulate.interestAmount
                            | currency: currency[0].code
                        }}
                      </div>
                    </div>

                    <div class="flex items-center mb-2 justify-between">
                      <div class="flex items-center">
                        <mat-icon
                          class="mx-1.5 text-lime mat-tm-icon mat-tm-icon-size-10"
                        >
                          lens
                        </mat-icon>
                        <div class="text-sm text-compass-60">
                          {{
                            'application-details.credit-limit-early-repayment.earlyRepaymentFee'
                              | transloco
                          }}
                        </div>
                      </div>
                      <div class="text-base text-compass-87 font-bold">
                        {{
                          earlyRepaymentSimulate.earlyRepaymentFee
                            | currency: currency[0].code
                        }}
                      </div>
                    </div>

                    <!-- <div class="flex items-center mb-2 justify-between">
                  <div class="flex items-center">
                    <mat-icon
                      class="mx-1.5 text-lobster mat-tm-icon mat-tm-icon-size-10"
                    >
                      lens
                    </mat-icon>
                    <div class="text-sm text-compass-60">Portfolio fees</div>
                  </div>
                  <div class="text-base text-compass-87 font-bold">$100</div>
                </div> -->
                  </div>
                </div>
              </div>

              <!-- <div class="grid col-span-2 mx-4">
              <div class="font-semibold font">Documents</div>
            </div> -->
            </div>
          </div>
        </ng-container>
      </ng-container>

      <div class="mb-6 main-card" *ngIf="simulationDetails.totalDebtAmount > 0">
        <div class="border-compass">
          <div class="grid grid-cols-12 items-center gap-4 mb-2">
            <div class="col-span-8 flex text-compass-87 font-semibold text-2xl">
              <div>
                {{
                  'application-details.credit-limit-early-repayment.overdue'
                    | transloco
                }}
              </div>
              <ng-container
                *ngIf="simulationDetails.creditLimit?.overdue as overdue"
              >
                <e2e-overdue-tooltip
                  class="leading-[0] self-center"
                  [totalOverdue]="overdue"
                  tooltipPosition="left"
                ></e2e-overdue-tooltip>
              </ng-container>
            </div>
            <div class="col-span-2 text-right me-4">
              <div
                *ngIf="
                  simulationDetails.repaymentType === repaymentTypes.Full ||
                    (simulationDetails.repaymentType ===
                      repaymentTypes.Partial &&
                      simulationDetails.amountToRepay &&
                      simulationDetails.amountToRepay >=
                        simulationDetails.totalDebtAmount);
                  else partialMode
                "
                class="operation-status early-repayment-status-full"
              >
                {{
                  'application-details.credit-limit-early-repayment.full'
                    | transloco
                    | uppercase
                }}
              </div>

              <ng-template #partialMode>
                <div class="operation-status early-repayment-status-partial">
                  {{
                    'application-details.credit-limit-early-repayment.partial'
                      | transloco
                      | uppercase
                  }}
                </div>
              </ng-template>
            </div>
            <div
              class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
            >
              <div
                *ngIf="
                  simulationDetails.amountToRepay &&
                    simulationDetails.amountToRepay > 0 &&
                    simulationDetails.amountToRepay <
                      simulationDetails.totalDebtAmount;
                  else partialModeTotalDebtAmount
                "
              >
                {{
                  simulationDetails.amountToRepay | currency: currency[0].code
                }}
              </div>
              <ng-template #partialModeTotalDebtAmount>
                {{
                  simulationDetails.totalDebtAmount | currency: currency[0].code
                }}
              </ng-template>
            </div>
          </div>

          <div class="bg-maWhite rounded-lg px-6 py-4">
            <div class="grid grid-cols-12 items-center gap-4">
              <div class="col-span-3 text-compass font-semibold text-lg">
                {{
                  'application-details.credit-limit-early-repayment.overdueRepayment'
                    | transloco
                }}
              </div>
              <div class="col-span-5 text-compass-87 font-semibold text-xs">
                <ng-container
                  *ngIf="getApplicationStatus$ | async as getApplicationStatus"
                >
                  <!-- <button
                    *ngIf="
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openDebtRepaymentTable(
                        simulationDetails.totalDebtAmount,
                        simulationDetails.effectiveDate,
                        simulationDetails.creditLimit?.id
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button> -->

                  <div
                    *ngIf="
                      simulationDetails.amountToRepay &&
                        simulationDetails.amountToRepay > 0 &&
                        simulationDetails.amountToRepay <
                          simulationDetails.totalDebtAmount;
                      else partialModeButtonSimulate
                    "
                  >
                    <button
                      *ngIf="
                        getApplicationStatus !== applicationStatuses.Completed
                      "
                      mat-button
                      type="button"
                      color="primary"
                      class="!rounded-lg !h-[35px]"
                      (click)="
                        openDebtRepaymentTable(
                          simulationDetails.amountToRepay,
                          simulationDetails.effectiveDate,
                          simulationDetails.creditLimit?.id
                        )
                      "
                    >
                      <div class="flex items-center">
                        <span class="me-2 pt-2">
                          <mat-icon svgIcon="icon-eye"></mat-icon>
                        </span>
                        <span class="text-sm font-medium">
                          {{
                            'general.buttons.viewSimulation'
                              | transloco
                              | titlecase
                          }}
                        </span>
                      </div>
                    </button>
                  </div>
                  <ng-template #partialModeButtonSimulate>
                    <button
                      *ngIf="
                        getApplicationStatus !== applicationStatuses.Completed
                      "
                      mat-button
                      type="button"
                      color="primary"
                      class="!rounded-lg !h-[35px]"
                      (click)="
                        openDebtRepaymentTable(
                          simulationDetails.totalDebtAmount,
                          simulationDetails.effectiveDate,
                          simulationDetails.creditLimit?.id
                        )
                      "
                    >
                      <div class="flex items-center">
                        <span class="me-2 pt-2">
                          <mat-icon svgIcon="icon-eye"></mat-icon>
                        </span>
                        <span class="text-sm font-medium">
                          {{
                            'general.buttons.viewSimulation'
                              | transloco
                              | titlecase
                          }}
                        </span>
                      </div>
                    </button>
                  </ng-template>
                </ng-container>
              </div>
              <div class="col-span-2 text-right">
                <div
                  *ngIf="
                    simulationDetails.repaymentType === repaymentTypes.Full ||
                      (simulationDetails.repaymentType ===
                        repaymentTypes.Partial &&
                        simulationDetails.amountToRepay &&
                        simulationDetails.amountToRepay >=
                          simulationDetails.totalDebtAmount);
                    else partialModeRepayment
                  "
                  class="operation-status early-repayment-status-full"
                >
                  {{
                    'application-details.credit-limit-early-repayment.full'
                      | transloco
                      | uppercase
                  }}
                </div>

                <ng-template #partialModeRepayment>
                  <div class="operation-status early-repayment-status-partial">
                    {{
                      'application-details.credit-limit-early-repayment.partial'
                        | transloco
                        | uppercase
                    }}
                  </div>
                </ng-template>
              </div>
              <div
                class="col-span-2 text-compass-87 font-semibold text-lg text-right me-2"
              >
                <div
                  *ngIf="
                    simulationDetails.amountToRepay &&
                      simulationDetails.amountToRepay > 0 &&
                      simulationDetails.amountToRepay <
                        simulationDetails.totalDebtAmount;
                    else partialModeRepaymentTotalDebtAmount
                  "
                >
                  {{
                    simulationDetails.amountToRepay | currency: currency[0].code
                  }}
                </div>
                <ng-template #partialModeRepaymentTotalDebtAmount>
                  {{
                    simulationDetails.totalDebtAmount
                      | currency: currency[0].code
                  }}
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="
          simulationDetails.repaymentType === repaymentTypes.Full ||
          (simulationDetails.repaymentType === repaymentTypes.Partial &&
            simulationDetails.amountToRepay &&
            simulationDetails.amountToRepay > simulationDetails.totalDebtAmount)
        "
        class="mb-6 main-card"
      >
        <div
          *ngFor="let portfolio of simulationDetails.portfolios"
          class="border-compass mb-4"
        >
          <div class="grid grid-cols-12 items-center mb-2 gap-4">
            <div class="col-span-8 text-compass-87 font-semibold text-2xl">
              <span>
                {{ 'e2e.products.portfolio' | transloco }}
                {{ portfolio.referenceName }}
              </span>
              <tm-tooltip
                [escape]="false"
                [tooltipTemplateMessage]="portfolioTooltipTemplate"
              >
                <div #portfolioTooltipTemplate>
                  <div class="grid grid-cols-2 gap-2">
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.portfolioAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.portfolioAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.utilizedAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.utilizedAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.unutilizedAmount'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{
                        portfolio.unutilizedAmount | currency: currency[0].code
                      }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.purpose'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.purpose | transloco }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.effectiveDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.effectiveDate | date: dateFormat }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.endUtilizationDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.endUtilizationDate | date: dateFormat }}
                    </div>
                    <div class="text-sm font-light">
                      {{
                        'application-details.credit-limit-early-repayment.portfolioTooltip.maturityDate'
                          | transloco
                      }}
                    </div>
                    <div class="text-sm font-extrabold">
                      {{ portfolio.maturityDate | date: dateFormat }}
                    </div>
                  </div>
                </div>
              </tm-tooltip>
            </div>
            <div class="col-span-2 text-right me-4">
              <div
                class="operation-status"
                [ngClass]="
                  'early-repayment-status-' + portfolio.earlyRepaymentType
                    | lowercase
                "
              >
                {{
                  'application-details.credit-limit-early-repayment.' +
                    (portfolio.earlyRepaymentType | lowercase)
                    | transloco
                    | uppercase
                }}
              </div>
            </div>
            <div
              class="col-span-2 text-pacificBridge font-semibold text-xl text-right pe-6"
            >
              {{ portfolio.earlyRepaymentAmount | currency: currency[0].code }}
            </div>
          </div>

          <div
            class="bg-maWhite rounded-lg px-6 py-4 mb-4"
            *ngFor="let track of portfolio.tracks; let i = index"
          >
            <div
              class="grid grid-cols-12 items-center gap-4 border-b mb-4 pb-4"
            >
              <div class="col-span-3 text-lead font-black text-lg">
                <span>
                  {{ 'e2e.portfolioAndCollateral.track' | transloco }}
                  #{{ track.referenceName }}
                </span>
                <tm-tooltip
                  [escape]="false"
                  [tooltipTemplateMessage]="trackTooltipTemplate"
                >
                  <div #trackTooltipTemplate>
                    <div class="grid grid-cols-2 gap-2">
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.trackAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.trackAmount | currency: currency[0].code }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.utilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.utilizedAmount | currency: currency[0].code }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.trackTooltip.unutilizedAmount'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{
                          track.unutilizedAmount | currency: currency[0].code
                        }}
                      </div>
                      <div class="text-sm font-light">
                        {{
                          'application-details.credit-limit-early-repayment.product'
                            | transloco
                        }}
                      </div>
                      <div class="text-sm font-extrabold">
                        {{ track.product }}
                      </div>
                    </div>
                  </div>
                </tm-tooltip>
              </div>
              <div class="col-span-5 text-compass-87 font-semibold text-xs">
                {{
                  'application-details.credit-limit-early-repayment.product'
                    | transloco
                }}
                <span class="text-lead">
                  {{ track.product }}
                </span>
              </div>

              <div class="col-span-2 text-right">
                <div
                  class="operation-status"
                  [ngClass]="
                    'early-repayment-status-' + track.earlyRepaymentType
                      | lowercase
                  "
                >
                  {{
                    'application-details.credit-limit-early-repayment.' +
                      (track.earlyRepaymentType | lowercase)
                      | transloco
                      | uppercase
                  }}
                </div>
              </div>
              <div
                class="col-span-2 text-lead font-semibold text-lg text-right me-2"
              >
                {{ track.earlyRepaymentAmount | currency: currency[0].code }}
              </div>
            </div>

            <div
              *ngFor="let loan of track.loans; let loanIndex = index"
              class="grid grid-cols-12 mb-2 items-center gap-4"
            >
              <div
                class="col-span-3 text-compass font-semibold text-sm flex items-center"
              >
                <div>
                  <span>
                    {{ 'e2e.disbursement.loan' | transloco }}
                    #{{ loan.referenceName }}
                  </span>
                  <tm-tooltip
                    [escape]="false"
                    [tooltipTemplateMessage]="loanTooltipTemplate"
                  >
                    <div #loanTooltipTemplate>
                      <div class="grid grid-cols-2 gap-2">
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.loanAmount'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.loanAmount | currency: currency[0].code }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.outstandingPrincipal'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.outstandingPrincipal
                              | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.amountFullRepayment'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.amountFullRepayment
                              | currency: currency[0].code
                          }}
                        </div>
                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.disbursementDate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.disbursementDate | date: 'dd.MM.yyyy' }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.maturityDate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.maturityDate | date: 'dd.MM.yyyy' }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.status'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.status }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.numberOfPayments'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.numberOfPayments }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.interestType'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.interestType }}
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.regularInterestRate'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{
                            loan.regularInterestRate * 100 | number: '1.2-2'
                          }}%
                        </div>

                        <div class="text-sm font-light">
                          {{
                            'application-details.credit-limit-early-repayment.loanTooltip.financialSource'
                              | transloco
                          }}
                        </div>
                        <div class="text-sm font-extrabold">
                          {{ loan.financialSource }}
                        </div>
                      </div>
                    </div>
                  </tm-tooltip>
                </div>
              </div>
              <div class="col-span-5">
                <ng-container
                  *ngIf="getApplicationStatus$ | async as getApplicationStatus"
                >
                  <button
                    *ngIf="
                      loan.earlyRepaymentType === repaymentTypes.Full &&
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openFullRepaymentTable(
                        loan.id,
                        simulationDetails.effectiveDate,
                        simulationDetails.fees
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                  <button
                    *ngIf="
                      loan.earlyRepaymentType === repaymentTypes.Partial &&
                      getApplicationStatus !== applicationStatuses.Completed
                    "
                    mat-button
                    type="button"
                    color="primary"
                    class="!rounded-lg !h-[35px]"
                    (click)="
                      openPartialRepaymentTable(
                        loan.id,
                        simulationDetails.effectiveDate,
                        simulationDetails.fees,
                        loan.earlyRepaymentAmount,
                        simulationDetails.repaymentDistribution
                      )
                    "
                  >
                    <div class="flex items-center">
                      <span class="me-2 pt-2">
                        <mat-icon svgIcon="icon-eye"></mat-icon>
                      </span>
                      <span class="text-sm font-medium">
                        {{
                          'general.buttons.viewSimulation'
                            | transloco
                            | titlecase
                        }}
                      </span>
                    </div>
                  </button>
                </ng-container>
              </div>

              <div class="col-span-2 text-right">
                <div
                  class="operation-status"
                  [ngClass]="
                    'early-repayment-status-' + loan.earlyRepaymentType
                      | lowercase
                  "
                >
                  {{
                    'application-details.credit-limit-early-repayment.' +
                      (loan.earlyRepaymentType | lowercase)
                      | transloco
                      | uppercase
                  }}
                </div>
              </div>
              <div
                class="col-span-2 text-compass-87 font-semibold text-lg text-right me-2 relative"
              >
                <span
                  *ngIf="loan.earlyRepaymentType === repaymentTypes.Partial"
                >
                  {{ loan.earlyRepaymentAmount | currency: currency[0].code }}
                </span>
                <span *ngIf="loan.earlyRepaymentType === repaymentTypes.Full">
                  {{ loan.amountFullRepayment | currency: currency[0].code }}
                </span>

                <ng-container
                  *ngIf="
                    this.getEarlyRepaymentSimulate$
                      | async as earlyRepaymentSimulate
                  "
                >
                  <ng-container
                    *ngIf="
                      getTooltip(
                        loan.id,
                        earlyRepaymentSimulate.loans
                      ) as tooltipLoan
                    "
                  >
                    <tm-tooltip
                      class="absolute"
                      [escape]="false"
                      [tooltipPosition]="'left'"
                      [tooltipTemplateMessage]="simulateTooltipTemplate"
                    >
                      <div #simulateTooltipTemplate>
                        <div class="grid grid-cols-2 gap-2">
                          <div class="text-sm font-light">
                            {{
                              'e2e.simulatePreview.totalRepayment' | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              tooltipLoan.totalRepaymentAmount
                                | currency: currency[0].code
                            }}
                          </div>

                          <div class="text-sm font-light">
                            {{
                              'e2e.simulatePreview.principalToBePaid'
                                | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              tooltipLoan.principalToBePaid
                                | currency: currency[0].code
                            }}
                          </div>
                          <div class="text-sm font-light">
                            {{ 'e2e.simulatePreview.repaymentFee' | transloco }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              tooltipLoan.earlyRepaymentFee
                                | currency: currency[0].code
                            }}
                          </div>
                          <div class="text-sm font-light">
                            {{
                              'e2e.simulatePreview.interestAmount' | transloco
                            }}
                          </div>
                          <div class="text-sm font-extrabold">
                            {{
                              tooltipLoan.interestAmount
                                | currency: currency[0].code
                            }}
                          </div>
                        </div>
                      </div>
                    </tm-tooltip>
                  </ng-container>
                </ng-container>
              </div>
            </div>
            <!--end.loans-->
          </div>
          <!--end.tracks-->
        </div>
        <!--end.portfolios-->
      </div>
    </ng-container>
  </ng-container>
</ng-container>
