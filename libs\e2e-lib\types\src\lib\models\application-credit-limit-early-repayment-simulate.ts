import { E2eTotalOverdue } from './e2e-total-overdue';

export interface ApplicationCreditLimitEarlyRepaymentSimulate {
  effectiveDate: string;
  totalRepaymentAmount: number;
  principalToBePaid: number;
  earlyRepaymentFee: number;
  interestAmount: number;
  overdue: E2eTotalOverdue;
  loans: {
    id: number;
    totalRepaymentAmount: number;
    principalToBePaid: number;
    earlyRepaymentFee: number;
    interestAmount: number;
  };
}
