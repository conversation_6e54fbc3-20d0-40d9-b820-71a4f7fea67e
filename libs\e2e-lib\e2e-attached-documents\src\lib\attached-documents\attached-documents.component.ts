import { Component, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { TenantManagementLibPageActions } from '@tenant-management/lib/state';
import { DocumentTypes, TmDocument } from '@tenant-management/lib/types';

@Component({
  selector: 'e2e-attached-documents',
  templateUrl: './attached-documents.component.html',
  styleUrls: ['./attached-documents.component.scss'],
})
export class AttachedDocumentsComponent {
  // TODO: reuse this component where is possible for the attached documents

  @Input() documents: TmDocument[] = [];
  @Input() shownType!: DocumentTypes;
  @Input() showAllTypes = false;
  @Input() title = 'e2e.attachedDocuments.title';
  @Input() addBottomMargin = false;

  constructor(private store: Store) {}

  getDocument(refId: string) {
    this.store.dispatch(TenantManagementLibPageActions.getDocument({ refId }));
  }

  trackByFn(index: number, document: TmDocument) {
    return document.id;
  }
}
